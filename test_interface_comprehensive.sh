#!/bin/bash

# 全面接口测试脚本
# 测试所有帖子相关接口的缓存功能

API_BASE_URL="http://localhost:8801"
CONTENT_TYPE="Content-Type: application/json"

echo "=== 全面接口缓存测试 ==="

# 检查服务状态
echo "检查服务状态..."
curl -s --connect-timeout 5 "$API_BASE_URL" > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 服务未运行"
    exit 1
fi
echo "✅ 服务运行正常"

# 获取真实的帖子数据
echo -e "\n--- 获取测试数据 ---"
echo "获取帖子列表..."
list_response=$(curl -s -H "$CONTENT_TYPE" "$API_BASE_URL/web/v1/posts/list?page=1&page_size=5")
echo "列表响应: $(echo $list_response | jq -c '.data.list[0:2]' 2>/dev/null || echo $list_response | head -100)"

# 提取帖子ID
post_ids=($(echo $list_response | jq -r '.data.list[].id' 2>/dev/null | head -3))
if [ ${#post_ids[@]} -eq 0 ]; then
    echo "⚠️  无法获取帖子ID，使用默认测试ID"
    post_ids=("1947948424960729088" "test-post-2" "test-post-3")
fi

echo "使用的测试帖子ID: ${post_ids[@]}"

# 测试1：帖子详情接口缓存测试
echo -e "\n--- 测试1：帖子详情接口缓存 ---"

for post_id in "${post_ids[@]:0:2}"; do
    echo "测试帖子ID: $post_id"
    
    # 清除缓存
    redis-cli del "app_service:card_community:post:detail:$post_id" > /dev/null
    
    # 第一次请求
    echo "  第一次请求..."
    start_time=$(date +%s)
    response1=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
        "$API_BASE_URL/web/v1/posts/detail?id=$post_id")
    end_time=$(date +%s)
    
    http_code1=$(echo $response1 | cut -d'|' -f1 | tail -c 4)
    time1=$(echo $response1 | cut -d'|' -f2)
    body1=$(echo $response1 | sed 's/|[^|]*$//')
    
    echo "  状态码: $http_code1, 耗时: ${time1}s"
    
    # 第二次请求
    echo "  第二次请求..."
    response2=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
        "$API_BASE_URL/web/v1/posts/detail?id=$post_id")
    
    http_code2=$(echo $response2 | cut -d'|' -f1 | tail -c 4)
    time2=$(echo $response2 | cut -d'|' -f2)
    
    echo "  状态码: $http_code2, 耗时: ${time2}s"
    
    # 验证缓存
    cache_ttl=$(redis-cli ttl "app_service:card_community:post:detail:$post_id")
    if [ "$cache_ttl" != "-2" ]; then
        echo "  ✅ 缓存已创建，TTL: ${cache_ttl}秒"
    else
        echo "  ❌ 缓存未创建"
    fi
    
    echo ""
done

# 测试2：帖子列表接口缓存测试
echo -e "\n--- 测试2：帖子列表接口缓存 ---"

test_params=(
    "page=1&page_size=10"
    "page=1&page_size=5"
    "page=2&page_size=10"
)

for params in "${test_params[@]}"; do
    echo "测试参数: $params"
    
    # 生成缓存键
    cache_key="app_service:card_community:post:list:$(echo $params | sed 's/&/:/g' | sed 's/=/:/')"
    
    # 清除缓存
    redis-cli del "$cache_key" > /dev/null
    
    # 第一次请求
    echo "  第一次请求..."
    list_response1=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
        "$API_BASE_URL/web/v1/posts/list?$params")
    
    list_code1=$(echo $list_response1 | cut -d'|' -f1 | tail -c 4)
    list_time1=$(echo $list_response1 | cut -d'|' -f2)
    
    echo "  状态码: $list_code1, 耗时: ${list_time1}s"
    
    # 第二次请求
    echo "  第二次请求..."
    list_response2=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
        "$API_BASE_URL/web/v1/posts/list?$params")
    
    list_code2=$(echo $list_response2 | cut -d'|' -f1 | tail -c 4)
    list_time2=$(echo $list_response2 | cut -d'|' -f2)
    
    echo "  状态码: $list_code2, 耗时: ${list_time2}s"
    
    # 验证缓存（尝试多种可能的键格式）
    possible_keys=(
        "app_service:card_community:post:list:$(echo $params | sed 's/&/:/g')"
        "app_service:card_community:post:list:$(echo $params | tr '&=' ':')"
    )
    
    cache_found=false
    for key in "${possible_keys[@]}"; do
        cache_ttl=$(redis-cli ttl "$key" 2>/dev/null)
        if [ "$cache_ttl" != "-2" ] && [ "$cache_ttl" != "" ]; then
            echo "  ✅ 缓存已创建，键: $key, TTL: ${cache_ttl}秒"
            cache_found=true
            break
        fi
    done
    
    if [ "$cache_found" = false ]; then
        echo "  ⚠️  缓存键未找到，检查所有相关键..."
        redis-cli keys "*post:list*" | head -3
    fi
    
    echo ""
done

# 测试3：不存在帖子的空值缓存测试
echo -e "\n--- 测试3：空值缓存测试 ---"

non_existent_ids=("non-existent-$(date +%s)" "fake-post-123" "invalid-id-999")

for fake_id in "${non_existent_ids[@]}"; do
    echo "测试不存在的帖子ID: $fake_id"
    
    # 第一次请求
    echo "  第一次请求..."
    fake_response1=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
        "$API_BASE_URL/web/v1/posts/detail?id=$fake_id")
    
    fake_code1=$(echo $fake_response1 | cut -d'|' -f1 | tail -c 4)
    fake_time1=$(echo $fake_response1 | cut -d'|' -f2)
    
    echo "  状态码: $fake_code1, 耗时: ${fake_time1}s"
    
    # 检查空值缓存
    null_cache=$(redis-cli get "app_service:card_community:post:detail:$fake_id")
    if [ "$null_cache" = "__NULL_CACHE__" ]; then
        echo "  ✅ 空值缓存已创建"
        
        # 第二次请求
        echo "  第二次请求..."
        fake_response2=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
            "$API_BASE_URL/web/v1/posts/detail?id=$fake_id")
        
        fake_code2=$(echo $fake_response2 | cut -d'|' -f1 | tail -c 4)
        fake_time2=$(echo $fake_response2 | cut -d'|' -f2)
        
        echo "  状态码: $fake_code2, 耗时: ${fake_time2}s"
        
        # 检查TTL
        null_ttl=$(redis-cli ttl "app_service:card_community:post:detail:$fake_id")
        echo "  空值缓存TTL: ${null_ttl}秒"
    else
        echo "  ❌ 空值缓存未创建"
    fi
    
    echo ""
done

# 测试4：并发压力测试
echo -e "\n--- 测试4：并发压力测试 ---"

if [ ${#post_ids[@]} -gt 0 ]; then
    test_post_id="${post_ids[0]}"
    echo "使用帖子ID进行并发测试: $test_post_id"
    
    # 清除缓存
    redis-cli del "app_service:card_community:post:detail:$test_post_id" > /dev/null
    
    echo "启动20个并发请求..."
    temp_file="/tmp/concurrent_test_$$"
    
    for i in {1..20}; do
        (
            response=$(curl -s -w "%{http_code}|%{time_total}" -H "$CONTENT_TYPE" \
                "$API_BASE_URL/web/v1/posts/detail?id=$test_post_id")
            echo "$i:$response" >> "$temp_file"
        ) &
    done
    
    wait
    
    if [ -f "$temp_file" ]; then
        echo "并发测试结果："
        success_count=0
        total_time=0
        
        while IFS=':' read -r req_id response; do
            http_code=$(echo $response | cut -d'|' -f1 | tail -c 4)
            time_total=$(echo $response | cut -d'|' -f2)
            
            echo "  请求 $req_id: HTTP $http_code, 耗时 ${time_total}s"
            
            if [ "$http_code" = "200" ]; then
                success_count=$((success_count + 1))
                total_time=$(echo "$total_time + $time_total" | bc -l)
            fi
        done < "$temp_file"
        
        if [ $success_count -gt 0 ]; then
            avg_time=$(echo "scale=4; $total_time / $success_count" | bc -l)
            echo "  ✅ 成功: $success_count/20, 平均耗时: ${avg_time}s"
        fi
        
        rm -f "$temp_file"
    fi
fi

# 测试5：缓存键分析
echo -e "\n--- 测试5：缓存键分析 ---"

echo "当前Redis中的缓存键："
redis_keys=$(redis-cli keys "app_service:card_community:post:*")
if [ -n "$redis_keys" ]; then
    echo "$redis_keys" | while read key; do
        if [ -n "$key" ]; then
            ttl=$(redis-cli ttl "$key")
            size=$(redis-cli memory usage "$key" 2>/dev/null || echo "N/A")
            echo "  $key - TTL: ${ttl}s, 内存: ${size}bytes"
        fi
    done
else
    echo "  无缓存键"
fi

echo -e "\n=== 全面接口测试完成 ==="
