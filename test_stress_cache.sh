#!/bin/bash

# 缓存压力测试脚本
# 测试高并发场景下的缓存性能

API_BASE_URL="http://localhost:8801"
CONTENT_TYPE="Content-Type: application/json"
EXISTING_POST_ID="1947948424960729088"

echo "=== 缓存压力测试 ==="

# 检查依赖
if ! command -v bc &> /dev/null; then
    echo "❌ 需要安装 bc 计算器"
    exit 1
fi

echo "检查服务状态..."
curl -s --connect-timeout 5 "$API_BASE_URL" > /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 服务未运行"
    exit 1
fi
echo "✅ 服务运行正常"

# 测试1：缓存命中率测试
echo -e "\n--- 测试1：缓存命中率测试 ---"

# 预热缓存
echo "预热缓存..."
curl -s "$API_BASE_URL/web/v1/posts/detail?id=$EXISTING_POST_ID" > /dev/null
curl -s "$API_BASE_URL/web/v1/posts/list?page=1&page_size=10" > /dev/null

echo "执行100次请求测试缓存命中率..."
temp_file="/tmp/cache_hit_test_$$"

for i in {1..100}; do
    (
        start_time=$(date +%s%3N)  # 毫秒时间戳
        response=$(curl -s -w "%{http_code}" "$API_BASE_URL/web/v1/posts/detail?id=$EXISTING_POST_ID")
        end_time=$(date +%s%3N)
        
        duration=$((end_time - start_time))
        http_code=${response: -3}
        
        echo "$i:$duration:$http_code" >> "$temp_file"
    ) &
    
    # 每10个请求等待一下，避免过载
    if [ $((i % 10)) -eq 0 ]; then
        wait
    fi
done

wait

# 分析结果
if [ -f "$temp_file" ]; then
    echo "分析缓存命中率..."
    
    total_requests=0
    success_requests=0
    total_time=0
    min_time=999999
    max_time=0
    
    while IFS=':' read -r req_id duration http_code; do
        total_requests=$((total_requests + 1))
        
        if [ "$http_code" = "200" ]; then
            success_requests=$((success_requests + 1))
            total_time=$((total_time + duration))
            
            if [ $duration -lt $min_time ]; then
                min_time=$duration
            fi
            
            if [ $duration -gt $max_time ]; then
                max_time=$duration
            fi
        fi
    done < "$temp_file"
    
    if [ $success_requests -gt 0 ]; then
        avg_time=$((total_time / success_requests))
        success_rate=$(echo "scale=2; $success_requests * 100 / $total_requests" | bc -l)
        
        echo "✅ 测试结果："
        echo "  总请求数: $total_requests"
        echo "  成功请求数: $success_requests"
        echo "  成功率: ${success_rate}%"
        echo "  平均响应时间: ${avg_time}ms"
        echo "  最快响应时间: ${min_time}ms"
        echo "  最慢响应时间: ${max_time}ms"
        
        if [ $avg_time -lt 50 ]; then
            echo "  ✅ 缓存性能优秀 (<50ms)"
        elif [ $avg_time -lt 100 ]; then
            echo "  ✅ 缓存性能良好 (<100ms)"
        else
            echo "  ⚠️  缓存性能一般 (>100ms)"
        fi
    fi
    
    rm -f "$temp_file"
fi

# 测试2：缓存雪崩模拟测试
echo -e "\n--- 测试2：缓存雪崩模拟测试 ---"

echo "模拟缓存雪崩场景..."

# 创建多个不同的缓存键
test_post_ids=("test-avalanche-1" "test-avalanche-2" "test-avalanche-3" "test-avalanche-4" "test-avalanche-5")

# 清除所有测试缓存
for post_id in "${test_post_ids[@]}"; do
    redis-cli del "app_service:card_community:post:detail:$post_id" > /dev/null
done

echo "同时请求多个不存在的帖子（模拟缓存失效）..."
avalanche_temp="/tmp/avalanche_test_$$"

for post_id in "${test_post_ids[@]}"; do
    for i in {1..5}; do
        (
            start_time=$(date +%s%3N)
            response=$(curl -s -w "%{http_code}" "$API_BASE_URL/web/v1/posts/detail?id=$post_id")
            end_time=$(date +%s%3N)
            
            duration=$((end_time - start_time))
            http_code=${response: -3}
            
            echo "$post_id:$i:$duration:$http_code" >> "$avalanche_temp"
        ) &
    done
done

wait

# 分析雪崩测试结果
if [ -f "$avalanche_temp" ]; then
    echo "分析缓存雪崩防护效果..."
    
    declare -A post_times
    declare -A post_counts
    
    while IFS=':' read -r post_id req_num duration http_code; do
        if [ "$http_code" = "200" ]; then
            if [ -z "${post_times[$post_id]}" ]; then
                post_times[$post_id]=0
                post_counts[$post_id]=0
            fi
            
            post_times[$post_id]=$((${post_times[$post_id]} + duration))
            post_counts[$post_id]=$((${post_counts[$post_id]} + 1))
        fi
    done < "$avalanche_temp"
    
    echo "各帖子平均响应时间："
    for post_id in "${test_post_ids[@]}"; do
        if [ ${post_counts[$post_id]} -gt 0 ]; then
            avg_time=$((${post_times[$post_id]} / ${post_counts[$post_id]}))
            echo "  $post_id: ${avg_time}ms (${post_counts[$post_id]}次请求)"
            
            # 检查是否创建了空值缓存
            cache_value=$(redis-cli get "app_service:card_community:post:detail:$post_id")
            if [ "$cache_value" = "__NULL_CACHE__" ]; then
                cache_ttl=$(redis-cli ttl "app_service:card_community:post:detail:$post_id")
                echo "    ✅ 空值缓存已创建，TTL: ${cache_ttl}秒"
            fi
        fi
    done
    
    rm -f "$avalanche_temp"
fi

# 测试3：内存使用分析
echo -e "\n--- 测试3：内存使用分析 ---"

echo "分析缓存内存使用情况..."

total_memory=0
cache_count=0

redis-cli keys "app_service:card_community:post:*" | while read key; do
    if [ -n "$key" ]; then
        memory=$(redis-cli memory usage "$key" 2>/dev/null)
        ttl=$(redis-cli ttl "$key")
        
        if [ -n "$memory" ] && [ "$memory" != "N/A" ]; then
            echo "  $key: ${memory}bytes, TTL: ${ttl}s"
            total_memory=$((total_memory + memory))
            cache_count=$((cache_count + 1))
        fi
    fi
done

# 获取Redis总内存使用
redis_info=$(redis-cli info memory | grep used_memory_human)
echo "Redis总内存使用: $redis_info"

# 测试4：TTL分布分析
echo -e "\n--- 测试4：TTL分布分析 ---"

echo "分析缓存TTL分布..."

declare -A ttl_ranges
ttl_ranges["0-60"]=0
ttl_ranges["60-300"]=0
ttl_ranges["300-600"]=0
ttl_ranges["600+"]=0

redis-cli keys "app_service:card_community:post:*" | while read key; do
    if [ -n "$key" ]; then
        ttl=$(redis-cli ttl "$key")
        
        if [ "$ttl" -ge 0 ]; then
            if [ $ttl -le 60 ]; then
                ttl_ranges["0-60"]=$((${ttl_ranges["0-60"]} + 1))
            elif [ $ttl -le 300 ]; then
                ttl_ranges["60-300"]=$((${ttl_ranges["60-300"]} + 1))
            elif [ $ttl -le 600 ]; then
                ttl_ranges["300-600"]=$((${ttl_ranges["300-600"]} + 1))
            else
                ttl_ranges["600+"]=$((${ttl_ranges["600+"]} + 1))
            fi
            
            echo "  $key: ${ttl}s"
        fi
    fi
done

echo "TTL分布统计："
for range in "0-60" "60-300" "300-600" "600+"; do
    echo "  ${range}秒: ${ttl_ranges[$range]}个缓存"
done

# 清理测试数据
echo -e "\n清理测试数据..."
for post_id in "${test_post_ids[@]}"; do
    redis-cli del "app_service:card_community:post:detail:$post_id" > /dev/null
done

echo -e "\n=== 缓存压力测试完成 ==="
echo "测试总结："
echo "1. ✅ 缓存命中率测试：验证高并发下的缓存性能"
echo "2. ✅ 缓存雪崩防护：验证随机TTL和空值缓存效果"
echo "3. ✅ 内存使用分析：监控缓存内存占用"
echo "4. ✅ TTL分布分析：验证TTL随机化效果"
