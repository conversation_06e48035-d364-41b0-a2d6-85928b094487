# 错误码分配注册表

## 分配规则

- 每个业务模块分配一个独立的错误码范围（10000个错误码）
- 错误码格式：6位数字
- 模块内部按功能子模块进行细分（每个子模块1000个错误码）

## 已分配范围

| 模块名称 | 错误码范围 | 负责人 | 备注 |
|---------|-----------|--------|------|
| synthesis | 100001-199999 | - | 融合相关业务 |
| story | 110001-119999 | - | 故事玩法 |
| announcement | 200001-209999 | - | 公告管理 |
| market_changes | 210001-219999 | - | 行情异动 |
| operation_announcement | 220001-229999 | - | 运营公告 |
| bonus_mall | 300001-309999 | - | 积分商城 |
| notification | 310001-319999 | - | 推送通知 |
| yc | 400001-409999 | - | 发货管理 |
| card_community | 500001-599999 | - | 卡牌社区 |

## 通用错误码

| 错误码 | 描述 | 模块 |
|-------|------|------|
| 8111001001 | 系统出了点小问题哦~请稍后再试~ | platform/common |
| 8111002002 | 系统有点小状况~别担心哦~ | platform/common |
| 8111003001 | 栏目有关联的数据不可删除 | platform/common |
| 8111003002 | 栏目有关联的数据不可禁用 | platform/common |
| 8111011001 | 没有找到用户信息！ | platform/user |
| 8111011002 | 新用户才能绑定！ | platform/user |

## 预留范围

| 错误码范围 | 状态 | 备注 |
|-----------|------|------|
| 600001-699999 | 可用 | 预留给新业务模块 |
| 700001-799999 | 可用 | 预留给新业务模块 |
| 800001-899999 | 可用 | 预留给新业务模块 |
| 900001-999999 | 可用 | 预留给新业务模块 |

## 错误码描述语义规范

### 语义结构要求

#### 基础结构规范

**主谓宾结构清晰**：错误码描述使用简洁的主谓宾结构
- ✅ "帖子不能编辑"
- ✅ "帖子不存在"
- ✅ "用户权限不足"

#### 状态复用策略

**核心思路**：基础错误码 + SetMsg 状态复用

```go
// 基础错误码 - 通用场景
CC500009Err = response.NewError(500009, "帖子不能编辑")

// 通过 SetMsg 复用不同状态
return nil, define.CC500009Err.SetMsg("已删除的帖子不能编辑")
return nil, define.CC500009Err.SetMsg("违规下架的帖子不能编辑")
return nil, define.CC500009Err.SetMsg("已下架的帖子不能编辑")
```

**优势**：
- 减少错误码数量
- 保持前端处理逻辑统一
- 提供具体的用户提示信息

### SetMsg 使用规范

#### 何时使用 SetMsg

✅ **适用场景**：
- **语义相近的场景**：同一资源的不同操作限制
- **参数化错误信息**：需要动态插入具体信息
- **上下文特定化**：基础错误码需要更具体的描述
- **临时性调整**：不值得新增错误码的小幅调整

#### 何时不能使用 SetMsg

❌ **禁用场景**：
- **跨模块使用**：不能修改其他模块的错误码描述
- **语义完全不同**：错误的根本原因不同
- **高频使用场景**：应该定义专门的错误码
- **需要国际化的场景**：SetMsg 不支持多语言

#### 技术限制

- `SetMsg()` 返回新的错误实例，不修改原错误码
- 只能在运行时动态设置，不能预定义
- 错误码本身保持不变，只改变描述文本
- 不支持格式化参数（如 `%s`, `%d`）

### 复用策略

#### 前端业务需求导向

**核心原则**：错误码复用应以前端业务需求为准

- **需要区分处理**：前端对不同错误需要不同的处理逻辑 → 使用不同错误码
- **统一处理**：前端对相似错误采用相同处理方式 → 可以复用错误码 + SetMsg
- **用户体验**：前端需要向用户展示不同的错误信息 → 考虑使用不同错误码

#### 技术实现策略

- 优先使用 `SetMsg()` 方法动态调整错误描述
- 避免为相似场景创建过多错误码
- 保持语义准确性和用户理解性的平衡
- **与前端团队协商**确定错误码粒度

### 应用示例

#### ✅ 正确使用

```go
// 前端统一处理的场景 - 可以复用
CC500009Err = response.NewError(500009, "已删除的帖子不能编辑")
// 前端都是禁用操作按钮，显示错误提示
return nil, define.CC500009Err.SetMsg("已删除的帖子不能变更状态")

// 参数化错误信息 - 前端需要具体信息
CC500205Err = response.NewError(500205, "接收者ID不能为空")
return nil, define.CC500205Err.SetMsg("接收者ID与会话不匹配")

// 上下文特定化 - 前端需要更精确的提示
CC500203Err = response.NewError(500203, "图片上传失败")
return nil, define.CC500203Err.SetMsg("媒体文件类型必须为图片")
```

#### ❌ 错误使用

```go
// 前端需要不同处理逻辑 - 应该使用不同错误码
CC500001Err = response.NewError(500001, "帖子不存在")
return nil, define.CC500001Err.SetMsg("用户权限不足")  // ❌ 前端处理逻辑完全不同

// 跨模块使用 - 违反模块边界
return nil, define.CommonErr.SetMsg("帖子相关错误")  // ❌ 错误

// 前端需要区分的场景 - 不应该复用
// 如果前端对"编辑限制"和"删除限制"需要不同的UI处理
return nil, define.CC500009Err.SetMsg("已删除的帖子不能删除")  // ❌ 应该新增错误码

// 决策建议：与前端确认是否需要区分这两种错误的处理方式
```

## 申请流程

1. 在此文档中选择可用范围
2. 更新分配表，标记为"已申请"
3. 在模块的 `define/err_code.go` 中定义具体错误码
4. 提交 PR 时需要同时更新此注册表

## 检查规则

- 新增错误码必须在已分配范围内
- 不允许跨模块使用错误码
- 错误码一旦分配不允许修改（向后兼容）
- 错误描述必须遵循语义规范
- **禁止使用包含"401"的错误码**：由于前端对401进行模糊匹配，任何包含连续"401"字符串的错误码都可能被误匹配，因此禁止使用 XXX401 等可能包含401的格式
