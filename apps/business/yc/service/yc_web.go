package service

import (
	"app_service/apps/business/yc/define"
	"app_service/apps/business/yc/service/logic"
	"app_service/apps/platform/common/dal/model/mongodb"
	commonFacade "app_service/apps/platform/common/facade"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/pkg/middlewares/g/auth"
	"app_service/pkg/util"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"math"
	"sort"
	"sync"
	"time"
)

var shanghaiLoc *time.Location // 统一时区处理（如 UTC+8）

func init() {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	shanghaiLoc = loc
}

var (
	notBindYcError = errors.New("用户还未绑定云仓")
)

type statisticOverviewDeps struct {
	lastMarketPriceMap map[string]int32
	lastClosePriceMap  map[string]int32
	todaySaleOrderMap  map[string][]*mongdb.SaleOrder
}

func (s *Service) setStatisticOverviewDeps(deps *statisticOverviewDeps, userID string, issueItemIDs []string) error {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	once := sync.Once{} // 一旦有 error 就停止
	// 启动监控协程
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// 获取商品的最新成交价
	wg.Add(1)
	go func(ctx context.Context) {
		defer wg.Done()
		// 监听终止信号
		select {
		case <-ctx.Done():
			return
		default:
		}
		lastMarketPriceMap, err := issueFacade.GetLatestSellPriceMapByItemIDs(ctx, issueItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("获取当前用户所有商品的最新成交价失败: %v", err)
			once.Do(func() {
				errChan <- fmt.Errorf("获取当前用户所有商品的最新成交价失败: %w", err)
				cancel() // 触发全局取消
			})
			return
		}
		deps.lastMarketPriceMap = lastMarketPriceMap
	}(ctx)

	// 获取上一个交易日收盘价
	wg.Add(1)
	go func(ctx context.Context) {
		defer wg.Done()
		// 监听终止信号
		select {
		case <-ctx.Done():
			return
		default:
		}
		lastClosePriceMap, err := issueFacade.GetPreClosePriceMapByItemIDs(ctx, issueItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("获取上一个交易日收盘价失败: %v", err)
			once.Do(func() {
				errChan <- fmt.Errorf("获取上一个交易日收盘价失败: %w", err)
				cancel() // 触发全局取消
			})
			return
		}
		deps.lastClosePriceMap = lastClosePriceMap
	}(ctx)

	// 获取当天成交订单
	wg.Add(1)
	go func(ctx context.Context) {
		defer wg.Done()
		// 监听终止信号
		select {
		case <-ctx.Done():
			return
		default:
		}
		todaySaleOrderMap, err := issueFacade.GetTodaySaleOrderMapByItemIDs(ctx, userID, issueItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("获取当天成交订单失败失败: %v", err)
			once.Do(func() {
				errChan <- fmt.Errorf("获取当天成交订单失败失败: %w", err)
				cancel() // 触发全局取消
			})
			return
		}
		deps.todaySaleOrderMap = todaySaleOrderMap
	}(ctx)

	// 等待首个错误或全部完成
	select {
	case err := <-errChan:
		return err
	case <-ctx.Done():
		return errors.New("setStatisticOverviewDeps 操作被取消")
	}
}
func (s *Service) GetWebStatisticOverview(req *define.GetWebStatisticOverviewReq) (*define.GetWebStatisticOverviewResp, error) {
	userInfo, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil, errors.New("获取用户信息失败")
	}
	userID := userInfo.Id
	// 从云仓获取统计数据
	countUserItems, err := s.getCountUserItems()
	if err != nil {
		if errors.Is(err, notBindYcError) {
			return &define.GetWebStatisticOverviewResp{}, nil
		}
		return nil, err
	}
	// 用户没有持仓商品直接返回
	if len(countUserItems) == 0 {
		return &define.GetWebStatisticOverviewResp{}, nil
	}
	// 得到所有的 issue_item 的 id
	var validIssueItemIDs []string
	for _, item := range countUserItems {
		validIssueItemIDs = append(validIssueItemIDs, item.ItemID)
	}
	// 获取依赖的数据
	deps := &statisticOverviewDeps{}
	err = s.setStatisticOverviewDeps(deps, userID, validIssueItemIDs)
	if err != nil {
		return nil, err
	}
	lastMarketPriceMap := deps.lastMarketPriceMap
	lastClosePriceMap := deps.lastClosePriceMap
	todaySaleOrderMap := deps.todaySaleOrderMap

	var holdQty int32                             // 持有数量
	var cost int64                                // 总成本
	var marketValue int64                         // 市值
	var lastClosePriceTotal int64                 // 持有商品上个交易日的收盘价x持有数量
	var todayTotalAmount int32                    // 当天已售出的商品总成交额
	var todayTotalAmountWithLastClosePrice int32  // 当天已售出商品上个交易日的收盘价x售出数量
	var holdAndTodayTotalWithLastClosePrice int32 // ((当前持有的数量+当天已售出的数量)x上个交易日的收盘价)
	// 遍历每个商品，进行相关计算
	for _, countItem := range countUserItems {
		holdQty += countItem.Count
		cost += countItem.Cost
		// 计算市值
		lastMarketPrice, ok := lastMarketPriceMap[countItem.ItemID]
		if !ok {
			return nil, errors.New("无法获取商品的最新成交价，item_id: " + countItem.ItemID)
		}
		marketValue += int64(lastMarketPrice) * int64(countItem.Count)

		mapBts, _ := json.Marshal(todaySaleOrderMap)
		log.Ctx(s.ctx).Debugf("user_id: %v, todaySaleOrderMap: %v", userID, string(mapBts))
		if lastClosePrice, ok := lastClosePriceMap[countItem.ItemID]; ok {
			lastClosePriceTotal += int64(lastClosePrice) * int64(countItem.Count)
			if todaySaleOrders, ok := todaySaleOrderMap[countItem.ItemID]; ok {
				todaySaleOrdersBts, _ := json.Marshal(todaySaleOrders)
				log.Ctx(s.ctx).Debugf("item_id: %v, todaySaleOrders: %v", countItem.ItemID, string(todaySaleOrdersBts))
				// 该商品当日有出售订单
				var todaySaleCountTotal int32 // 今天已售出的数量
				for _, todayOrder := range todaySaleOrders {
					todayTotalAmountWithLastClosePrice += lastClosePrice * todayOrder.SaleCount
					todaySaleCountTotal += todayOrder.SaleCount
				}
				log.Ctx(s.ctx).Debugf("GetWebStatisticOverview item_id: %v, lastClosePrice: %v", countItem.ItemID, lastClosePrice)
				log.Ctx(s.ctx).Debugf("GetWebStatisticOverview item_id: %v, todaySaleCountTotal: %v", countItem.ItemID, todaySaleCountTotal)
				holdAndTodayTotalWithLastClosePrice += (countItem.Count + todaySaleCountTotal) * lastClosePrice
			} else {
				// 该商品当日没有出售订单，直接用持有数量*上个交易日的收盘价
				holdAndTodayTotalWithLastClosePrice += countItem.Count * lastClosePrice
			}
		}
		// 计算当天已售商品的总成交额
		if todaySaleOrders, ok := todaySaleOrderMap[countItem.ItemID]; ok {
			for _, todayOrder := range todaySaleOrders {
				todayTotalAmount += todayOrder.OrderAmount
			}
		}
	}
	// 计算今日盈亏
	// 今日盈亏:(持仓市值-(持有商品上个交易日的收盘价x持有数量))+(当天已售出的商品总成交额-(当天已售出商品上个交易日的收盘价x售出数量))
	// 以 + 为分界线，左边的值为 a，右边的值为 b
	a := marketValue - lastClosePriceTotal
	log.Ctx(s.ctx).Debugf("user_id: %v, todayTotalAmount: %v", userID, todayTotalAmount)
	log.Ctx(s.ctx).Debugf("user_id: %v, todayTotalAmountWithLastClosePrice: %v", userID, todayTotalAmountWithLastClosePrice)
	b := todayTotalAmount - todayTotalAmountWithLastClosePrice
	todayProfitLossValue := a + int64(b)
	// 计算今日盈亏百分比
	// 今日盈亏百分比:今日盈亏 /((当前持有的数量+当天已售出的数量)x上个交易日的收盘价)x%
	if holdAndTodayTotalWithLastClosePrice == 0 {
		holdAndTodayTotalWithLastClosePrice = 100 // 单位是（分）
	}
	log.Ctx(s.ctx).Debugf("GetWebStatisticOverview holdAndTodayTotalWithLastClosePrice: %v", holdAndTodayTotalWithLastClosePrice)
	// 计算今日盈亏百分比
	originPercent := decimal.NewFromInt(todayProfitLossValue).
		Div(decimal.NewFromInt32(holdAndTodayTotalWithLastClosePrice)).
		Mul(decimal.NewFromInt32(100))
	roundedPercent := originPercent.Round(3)
	// 如果保留 3 位小数后的值为 0，则需要特殊处理：0.000123 -> 0.001 , -0.000456 -> -0.001
	if roundedPercent.IsZero() && !originPercent.IsZero() {
		if originPercent.GreaterThan(decimal.Zero) {
			roundedPercent = decimal.NewFromFloat(0.001)
		} else {
			roundedPercent = decimal.NewFromFloat(-0.001)
		}
	}
	todayProfitLossPercentageValue, _ := roundedPercent.Float64()
	result := &define.GetWebStatisticOverviewResp{
		HoldQuantity:              holdQty,
		TotalCost:                 cost,
		TotalMarketValue:          marketValue,
		TotalProfitLossValue:      marketValue - cost,
		TodayProfitLossValue:      todayProfitLossValue,
		TodayProfitLossPercentage: todayProfitLossPercentageValue,
	}
	return result, nil
}

// getOpenUserID 获取 open_user_id 用于请求云仓
func (s *Service) getOpenUserID() (*string, error) {
	userInfo, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil, errors.New("获取用户信息失败")
	}
	userID := userInfo.Id
	openUserID, err := userFacade.GetOpenUserId(s.ctx, userID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取云仓 id 失败: %v", err)
		return nil, err
	}

	return &openUserID, nil
}

// getCountUserItems 从云仓拉取当前用户的所有商品的统计数据
func (s *Service) getCountUserItems() ([]*yc_open.CountUserItemsRes, error) {
	openUserID, err := s.getOpenUserID()
	if err != nil {
		return nil, err
	}
	if openUserID == nil || *openUserID == "" {
		return nil, notBindYcError
	}
	// 获取所有的商品 id
	allIssueItemIDs, err := issueFacade.GetAllValidIssueItemIDs(s.ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("所有商品 id 失败: %v", err)
		return nil, err
	}
	// 从云仓拉取数据
	countUserItems, err := yc_open.CountUserItems(s.ctx, *openUserID, allIssueItemIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("从云仓拉取数据失败: %v", err)
		return nil, err
	}

	return countUserItems, nil
}

func (s *Service) GetWebAllItems(req *define.GetWebAllItemsReq) (*define.GetWebAllItemsResp, error) {
	countUserItems, err := s.getCountUserItems()
	if err != nil {
		if errors.Is(err, notBindYcError) {
			return &define.GetWebAllItemsResp{}, nil
		}
		return nil, err
	}
	// 用户没有持仓商品直接返回
	if len(countUserItems) == 0 {
		return &define.GetWebAllItemsResp{}, nil
	}
	// 当前用户的 item_id 列表
	var validIssueItemIDs []string
	for _, item := range countUserItems {
		validIssueItemIDs = append(validIssueItemIDs, item.ItemID)
	}
	// 获取用户持有的所有商品数据，用于后续组装
	issueItemMap, err := issueFacade.GetIssueItemMap(s.ctx, validIssueItemIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取当前用户所有 issue_item 数据失败: %v", err)
		return nil, err
	}
	// 获取商品的最新成交价
	lastMarketPriceMap, err := issueFacade.GetLatestSellPriceMapByItemIDs(s.ctx, validIssueItemIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取当前用户所有商品的最新成交价: %v", err)
		return nil, err
	}
	// 组装数据
	items := make([]define.Item, 0)
	for _, countItem := range countUserItems {
		issueItem, ok := issueItemMap[countItem.ItemID]
		if !ok {
			msg := "无效的商品，item_id: " + countItem.ItemID
			log.Ctx(s.ctx).Error(msg)
			return nil, errors.New(msg)
		}
		lastMarketPrice, ok := lastMarketPriceMap[countItem.ItemID]
		if !ok {
			msg := "无法获取商品的最新成交价，item_id: " + countItem.ItemID
			log.Ctx(s.ctx).Error(msg)
			return nil, errors.New(msg)
		}
		marketValue := lastMarketPrice * countItem.Count
		profitLossValue := int64(marketValue) - countItem.Cost
		cost := decimal.NewFromInt(countItem.Cost)
		if cost.IsZero() {
			// 如果总成本为 0，则设置为 100（分），即市值多少用户就赚了多少
			cost = decimal.NewFromInt32(100)
		}
		d := decimal.NewFromInt(profitLossValue).Div(cost).Mul(decimal.NewFromInt32(100)).Round(2)
		profitLossValuePercent, _ := d.Float64()
		items = append(items, define.Item{
			ItemID:               countItem.ItemID,
			ItemName:             issueItem.ItemName,
			IconURL:              issueItem.ImageURL,
			HoldQuantity:         countItem.Count,                      // 持有数量
			TotalCost:            countItem.Cost,                       // 总成本
			LastMarketPrice:      lastMarketPriceMap[countItem.ItemID], // 最新成交价
			MarketValue:          marketValue,                          // 市值
			ProfitLossValue:      profitLossValue,                      // 持仓盈亏
			ProfitLostPercentage: profitLossValuePercent,               // 持仓盈亏百分比
			DeliveryTime:         issueItem.DeliveryTime,
			SynthesisStatus:      issueItem.Synthesis.Status,
			StoryStatus:          issueItem.Story.Status,
		})
	}

	// 按照持仓盈亏倒序排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].ProfitLossValue > items[j].ProfitLossValue
	})

	return &define.GetWebAllItemsResp{
		Items: items,
	}, nil
}

type flowStatusResult struct {
	flowStatus        define.FlowStatusEnum
	daysUntilSellable int32
}

// calculateFlowStatus 计算流转状态
func calculateFlowStatus(ycUserItem *yc_open.GetUserItemListResItem, issueItem *mongdb.IssueItem,
	saleOrderMap map[string]*mongdb.SaleOrder, cooldownDay int32) flowStatusResult {
	userItemID := ycUserItem.ID

	// 计算融合状态
	if ycUserItem.TradeInfo.FusionStatus == 1 {
		return flowStatusResult{
			flowStatus: define.FlowStatusFused,
		}
	}
	if ycUserItem.TradeInfo.StoryStatus == 1 {
		return flowStatusResult{
			flowStatus: define.FlowStatusStory,
		}
	}

	// 计算禁止流转
	if issueItem.CirculationStatus == mongdb.CirculationStatusProhibit {
		// 禁止流转
		return flowStatusResult{
			flowStatus: define.FlowStatusForbidden,
		}
	}
	// 超过流通时间也算禁止流转
	now := time.Now()
	if issueItem.CirculationEnd != nil && now.After(*issueItem.CirculationEnd) {
		return flowStatusResult{
			flowStatus: define.FlowStatusForbidden,
		}
	}

	// 有订单，转换订单状态
	if saleOrder, exists := saleOrderMap[userItemID]; exists {
		statusMap := map[mongdb.SaleOrderStatusEnum]define.FlowStatusEnum{
			mongdb.SaleOrderStatusSell: define.FlowStatusOrderSell, // 出售中
			//mongdb.SaleOrderStatusClose:   define.FlowStatusOrderClose,   // 已取消
			//mongdb.SaleOrderStatusSellOut: define.FlowStatusOrderSellOut, // 已出售
			//mongdb.SaleOrderStatusOff:     define.FlowStatusOrderOff,     // 已下架
			mongdb.SaleOrderStatusSelling: define.FlowStatusOrderSelling, // 交易中
		}
		if flowStatus, exists := statusMap[mongdb.SaleOrderStatusEnum(saleOrder.OrderStatus)]; exists {
			return flowStatusResult{
				flowStatus: flowStatus,
			}
		}
	}

	// x 日可售
	if issueItem.CirculationStatus == mongdb.CirculationStatusAllow &&
		issueItem.CirculationStart != nil &&
		issueItem.CirculationEnd != nil {
		days := calculateDaysUntilSellable(*issueItem.CirculationStart, *issueItem.CirculationEnd, ycUserItem.CreatedAt, cooldownDay)
		if days > 0 {
			return flowStatusResult{
				flowStatus:        define.FlowStatusCountdown,
				daysUntilSellable: days,
			}
		} else if days == -1 {
			// 禁止流转
			return flowStatusResult{
				flowStatus: define.FlowStatusForbidden,
			}
		}
	}

	// 默认返回“可流转”
	return flowStatusResult{
		flowStatus: define.FlowStatusAvailable,
	}
}

func calculateDaysUntilSellable(circulationStart, circulationEnd, userItemCreatedAt time.Time, coolDownDay int32) int32 {
	// 强制转换为上海时区
	convertToShanghai := func(t time.Time) time.Time {
		return t.In(shanghaiLoc)
	}

	// 获取当前时间
	now := time.Now()
	// 标准化时间变量（全部转为上海时区日期）
	saleStartDay := util.GetStartOfDay(convertToShanghai(circulationStart))
	saleEndDay := util.GetStartOfDay(convertToShanghai(circulationEnd))
	createdDay := util.GetStartOfDay(convertToShanghai(userItemCreatedAt))
	currentDay := util.GetStartOfDay(convertToShanghai(now))
	log.Infof("saleStartDay: %v, saleEndDay: %v, createdDay: %v, currentDay: %v", saleStartDay, saleEndDay, createdDay, currentDay)

	// 闭区间判断（文档3时间比较方案）
	isBetween := func(t, start, end time.Time) bool {
		return (t.Equal(start) || t.After(start)) && (t.Equal(end) || t.Before(end))
	}

	// 场景1：创建时间在销售周期内
	if isBetween(createdDay, saleStartDay, saleEndDay) {
		if currentDay.After(saleEndDay) {
			return -1
		}

		// 计算已过天数
		duration := currentDay.Sub(createdDay)
		daysPassed := int32(math.Round(duration.Hours() / 24))
		daysToSell := coolDownDay - daysPassed
		log.Infof("daysPassed: %v, daysToSell: %v", daysPassed, daysToSell)

		// 边界处理
		if daysToSell < 0 {
			daysToSell = 0
		}

		// 检查预测结束时间[3](@ref)
		projectedEnd := currentDay.AddDate(0, 0, int(daysToSell))
		log.Infof("projectedEnd: %v", projectedEnd)
		if projectedEnd.After(saleEndDay) {
			return -1
		}
		return daysToSell
	}

	// 场景2：创建时间早于销售开始日
	if createdDay.Before(saleStartDay) {
		if currentDay.After(saleEndDay) {
			return -1
		}

		if isBetween(currentDay, saleStartDay, saleEndDay) {
			return 0
		}

		// 计算到销售开始日的天数[6](@ref)
		duration := saleStartDay.Sub(currentDay)
		daysRemaining := int32(math.Round(duration.Hours() / 24))
		return daysRemaining
	}

	// 其他情况（创建时间晚于销售结束日）
	return -1
}

func (s *Service) GetWebUserItemList(req *define.GetWebUserItemListReq) (*define.GetWebUserItemListResp, error) {
	openUserID, err := s.getOpenUserID()
	if err != nil {
		return nil, err
	}
	itemID := s.ctx.(*gin.Context).Param("item_id")
	userItems, err := yc_open.GetUserItemList(s.ctx, *openUserID, []string{itemID}, yc_open.GetUserItemListReqOptions{
		Page:     req.Page,
		PageSize: req.PageSize,
		Sort:     req.Sort,
	})
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetWebUserItemList 从云仓获取 user_item_list 失败: %v", err)
		return nil, err
	}
	// 用户没有持仓商品直接返回
	if len(userItems) == 0 {
		return &define.GetWebUserItemListResp{}, nil
	}
	var userItemIDs []string
	for _, item := range userItems {
		userItemIDs = append(userItemIDs, item.ID)
	}
	// 查询当前列表数据的订单
	saleOrderMap, err := issueFacade.GetSaleOrderMapByUserItemIDs(s.ctx, userItemIDs)
	if err != nil {
		return nil, err
	}
	issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, itemID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetWebUserItemList 获取 issue_item 失败: %v, item_id: %s", err, itemID)
		return nil, err
	}
	// 查询交易频次配置
	var coolDownDay int32 = 3
	tradeFrequencyStorageData, err := commonFacade.GetStorageDataByKey(s.ctx, "custom.trade_frequency")
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
	}
	log.Ctx(s.ctx).Debugf("tradeFrequencyStorageData.Value: %v", tradeFrequencyStorageData.Value)
	tradeFrequencyBts, err := json.Marshal(tradeFrequencyStorageData.Value)
	if err != nil {
		log.Ctx(s.ctx).Errorf("tradeFrequencyStorageData.Value Marshal error: %v", err)
	} else {
		var cfg *mongodb.CustomTradeFrequencyConfig
		err = json.Unmarshal(tradeFrequencyBts, &cfg)
		if err != nil {
			log.Ctx(s.ctx).Debugf("tradeFrequencyStorageData.Value Unmarshal error: %v", err)
		} else {
			log.Ctx(s.ctx).Debugf("tradeFrequencyStorageData.Value: %v", cfg)
			coolDownDay = cfg.CoolDownDay
		}
	}
	// 查询交易服务费配置
	var ratio int32 = 3
	tradeFeeStorageData, err := commonFacade.GetStorageDataByKey(s.ctx, "custom.trade_fee")
	if err != nil {
		if !errors.Is(err, mongo.ErrNoDocuments) {
			return nil, err
		}
	}
	log.Ctx(s.ctx).Debugf("tradeFeeStorageData.Value: %v", tradeFeeStorageData.Value)
	tradeFeeBts, err := json.Marshal(tradeFeeStorageData.Value)
	if err != nil {
		log.Ctx(s.ctx).Errorf("tradeFeeStorageData.Value Marshal error: %v", err)
	} else {
		var cfg *mongodb.CustomTradeFeeConfig
		err = json.Unmarshal(tradeFeeBts, &cfg)
		if err != nil {
			log.Ctx(s.ctx).Debugf("tradeFeeStorageData.Value Unmarshal error: %v", err)
		} else {
			log.Ctx(s.ctx).Debugf("tradeFeeStorageData.Value: %v", cfg)
			ratio = cfg.Ratio
		}
	}

	userItemList := make([]*define.UserItem, 0)
	for _, ycUserItem := range userItems {
		userItem := &define.UserItem{
			ID:           ycUserItem.ID,
			ItemID:       ycUserItem.ItemID,
			BuyPrice:     ycUserItem.TradeInfo.BuyPrice,
			BuyTime:      ycUserItem.TradeInfo.BuyTime,
			ReceiveType:  ycUserItem.Extends.ReceiveType,
			CreatedAt:    ycUserItem.CreatedAt,
			StoryStatus:  ycUserItem.TradeInfo.StoryStatus,
			FusionStatus: ycUserItem.TradeInfo.FusionStatus,
		}
		flowStatusData := calculateFlowStatus(ycUserItem, issueItem, saleOrderMap, coolDownDay)
		userItem.FlowStatus = flowStatusData.flowStatus
		userItem.DaysUntilSellable = flowStatusData.daysUntilSellable

		userItemList = append(userItemList, userItem)
	}
	userItemId2ReleaseTime, err := logic.GetReleaseTimeByUserItemIds(s.ctx, userItemList)
	if err != nil {
		return nil, err
	}
	logic.SetUserItemsFlowStatus(s.ctx, userItemList, userItemId2ReleaseTime, issueItem)
	// 获取商品出售时的费率
	result := &define.GetWebUserItemListResp{
		Tip:          ratio,
		UserItemList: userItemList,
	}

	return result, nil
}
