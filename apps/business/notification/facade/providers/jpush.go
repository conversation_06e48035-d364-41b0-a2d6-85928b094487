package providers

import (
	"app_service/apps/business/notification/dal/model"
	"app_service/apps/business/notification/define"
	"app_service/apps/business/notification/define/enums"
	"app_service/apps/business/notification/repo"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"bufio"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/device/platform"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/file"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/push"
	"github.com/calvinit/jiguang-sdk-go/api/jpush/push/notification/alert"
	"gorm.io/datatypes"
	"os"
	"time"
)

type JPushProvider struct {
	client push.APIv3
}

func NewJPushProvider(cli push.APIv3) *JPushProvider {
	return &JPushProvider{
		client: cli,
	}
}

func (a *JPushProvider) Name() string {
	return "jpush"
}

func (a *JPushProvider) SupportedPlatforms() []string {
	return []string{
		enums.PushPlatformAndroid.Val(),
		enums.PushPlatformIOS.Val(),
		enums.PushPlatformQuickApp.Val(),
		enums.PushPlatformHarmonyOS.Val(),
	}
}

func (a *JPushProvider) Send(ctx context.Context, message define.PushMessage, relateInfo define.PushRelateInfo) (*define.PushResult, error) {
	logPrefix := "JPushProvider.Send()"
	startTime := util.Now()
	pushResult := &define.PushResult{}
	params := &push.SendParam{}
	// 平台设置
	if len(message.Platforms) > 0 {
		params.Platform = message.Platforms
	} else {
		params.Platform = platform.All
	}

	// 接收听众
	regFileID := ""
	if message.AudienceType == enums.PushAudienceTypeAll {
		params.Audience = push.BroadcastAuds
	} else if message.AudienceType == enums.PushAudienceTypePushID {
		params.Audience = push.Audience{
			RegistrationIDs: message.PushIDs,
		}
	} else {
		audience := &push.Audience{}
		pushDeviceSchema := repo.GetQuery().PushDevice
		// 根据不同的听众类型查询符合条件的 push_id
		qb := search.NewQueryBuilder().
			Eq(pushDeviceSchema.Status, enums.PushDeviceStatusOnline.Val()).
			Eq(pushDeviceSchema.Provider, a.Name())
		switch message.AudienceType {
		case enums.PushAudienceTypeChannel:
			qb.In(pushDeviceSchema.ChannelID, message.ChannelIDs)
		case enums.PushAudienceTypeUser:
			qb.In(pushDeviceSchema.UserID, message.UserIDs)
		case enums.PushAudienceTypeDevice:
			qb.In(pushDeviceSchema.DeviceID, message.DeviceIDs)
		}
		qw := qb.Build()
		deviceList, total, err := repo.NewPushDeviceRepo(pushDeviceSchema.WithContext(ctx).Select(pushDeviceSchema.PushID)).
			SelectPage(qw, 1, 1000)
		if err != nil {
			return nil, err
		}
		if total == 0 {
			return nil, define.NT310005Err
		}
		pushResult.SendTotal = int32(total)
		// 超过 1000 采用文件上传
		if total <= 1000 {
			pushIDs := make([]string, 0)
			for _, device := range deviceList {
				pushIDs = append(pushIDs, device.PushID)
			}
			audience.RegistrationIDs = pushIDs
		} else {
			// 文件上传
			fileID, err := a.uploadFile(ctx, qw)
			if err != nil {
				return nil, err
			}
			regFileID = fileID
		}
		params.Audience = audience
	}

	// 推送的消息
	badgeAddNum := 1
	androidNotification := &push.AndroidNotification{
		Alert:       message.Content,
		Title:       message.Title,
		BadgeAddNum: &badgeAddNum,
	}
	if message.AndroidExtras != nil {
		androidNotification.Extras = message.AndroidExtras
	}
	iosAlert := &alert.IosAlert{
		Title: message.Title,
		Body:  message.Content,
	}
	iosBadge := "+1"
	iosNotification := &push.IosNotification{
		Alert: iosAlert,
		Sound: "default",
		Badge: &iosBadge,
	}
	if message.IOSExtras != nil {
		iosNotification.Extras = message.IOSExtras
	}
	notification := &push.Notification{
		Alert:   message.Content,
		Android: androidNotification,
		IOS:     iosNotification,
	}
	params.Notification = notification

	// iOS 环境配置
	apnsProd := true // iOS 打包时，测试环境和生产都是用同样的证书，设置为 true sit 环境才能收到推送
	if global.GlobalConfig.Service.Env == global.EnvDev {
		apnsProd = false
	}
	if message.ApnsProduction != nil {
		apnsProd = *message.ApnsProduction
	}
	opts := &push.Options{
		ApnsProduction: &apnsProd,
	}
	params.Options = opts

	var sendResult *push.SendResult
	var sendErr error
	payloadBts, _ := json.Marshal(params)
	if regFileID != "" {
		params.Audience = &push.Audience{
			File: &push.FileAudience{
				FileID: regFileID,
			},
		}
		sendResult, sendErr = a.client.SendByFile(ctx, params)
	} else {
		sendResult, sendErr = a.client.Send(ctx, params)
	}

	if sendErr != nil {
		log.Ctx(ctx).Errorf(logPrefix+" 极光推送失败，error：%v，result：%+v", sendErr, sendResult)
		return nil, sendErr
	} else if !sendResult.IsSuccess() {
		return nil, errors.New(string(sendResult.RawBody))
	}

	// 保存推送记录
	msgID := sendResult.MsgID
	payloadData := datatypes.JSON(payloadBts)
	audienceBts, _ := json.Marshal(params.Audience)
	audienceValue := datatypes.JSON(audienceBts)
	newRecord := &model.PushRecord{
		PushRecordID:  snowflakeutl.GenerateID(),
		Title:         message.Title,
		Content:       message.Content,
		RelateType:    relateInfo.RelateType,
		RelateScene:   relateInfo.RelateScene,
		RelateID:      relateInfo.RelateID,
		BatchID:       relateInfo.BatchID,
		MessageID:     msgID,
		Provider:      a.Name(),
		AudienceType:  message.AudienceType.Val(),
		AudienceValue: &audienceValue,
		Payload:       &payloadData,
		SendCount:     pushResult.SendTotal,
		Status:        enums.PushRecordStatusRequested.Val(),
	}
	pushRecordSchema := repo.GetQuery().PushRecord
	err := repo.NewPushRecordRepo(pushRecordSchema.WithContext(ctx)).Save(newRecord)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" 保存推送记录错误：%v，record: %+v", err, newRecord)
	}

	pushResult.SuccessCount = pushResult.SendTotal
	pushResult.Duration = time.Since(startTime)
	pushResult.MessageID = msgID
	return pushResult, nil
}

func createAudienceFile(ctx context.Context, qw *search.QueryWrapper) (string, error) {
	filename := fmt.Sprintf("tmp_jpush_audience_%d.txt", util.Now().Unix())
	// 创建文件（如果存在则清空内容）
	tmpFile, err := os.Create(filename)
	if err != nil {
		return "", err
	}
	defer func() {
		_ = tmpFile.Close()
	}()
	// 使用带缓冲的写入器提高性能
	writer := bufio.NewWriter(tmpFile)
	defer func() {
		_ = writer.Flush()
	}()

	batchSize := 10000
	for page := 1; page < 2000; page++ {
		offset := (page - 1) * batchSize
		pushDeviceSchema := repo.GetQuery().PushDevice
		deviceList, err := repo.NewPushDeviceRepo(pushDeviceSchema.WithContext(ctx).Select(pushDeviceSchema.PushID).Offset(offset).Limit(batchSize)).
			SelectList(qw)
		if err != nil {
			return "", err
		}

		if len(deviceList) == 0 {
			break
		}

		// 逐行写入
		for _, device := range deviceList {
			_, err = writer.WriteString(fmt.Sprintf("%s\n", device.PushID))
			if err != nil {
				return "", err
			}
		}
	}

	return filename, nil
}

func (a *JPushProvider) uploadFile(ctx context.Context, qw *search.QueryWrapper) (string, error) {
	// 先写入本地文件
	localFile, err := createAudienceFile(ctx, qw)
	if err != nil {
		return "", err
	}
	// 上传成功后删除临时文件
	defer func() {
		err = os.Remove(localFile)
		if err != nil {
			log.Ctx(ctx).Errorf("删除极光推送临时文件失败：%v", err)
		}
	}()

	result, err := a.client.UploadFileForRegistrationID(ctx, &file.FileUploadParam{File: localFile})
	if err != nil {
		return "", err
	} else if !result.IsSuccess() {
		return "", errors.New(string(result.RawBody))
	}

	return result.FileID, nil
}
