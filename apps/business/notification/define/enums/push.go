package enums

type PushAudienceTypeEnum string

func (r PushAudienceTypeEnum) Val() string {
	return string(r)
}

const (
	PushAudienceTypeAll     PushAudienceTypeEnum = "all"     // 推送给所有用户（广播推送）
	PushAudienceTypeChannel PushAudienceTypeEnum = "channel" // 按照渠道/终端类型推送
	PushAudienceTypeUser    PushAudienceTypeEnum = "user"    // 指定用户 id 推送
	PushAudienceTypeDevice  PushAudienceTypeEnum = "device"  // 指定设备 id 推送
	PushAudienceTypePushID  PushAudienceTypeEnum = "push_id" // 指定推送 id 推送
)

type PushPlatformEnum string

func (r PushPlatformEnum) Val() string {
	return string(r)
}

const (
	PushPlatformAndroid   PushPlatformEnum = "android"
	PushPlatformIOS       PushPlatformEnum = "ios"
	PushPlatformQuickApp  PushPlatformEnum = "quickapp" // 快应用
	PushPlatformHarmonyOS PushPlatformEnum = "hmos"     // 鸿蒙
)
