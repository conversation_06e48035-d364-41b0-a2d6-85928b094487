package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 管理端会话列表相关结构体
type (
	// GetConversationAdminListReq 获取管理端会话列表请求
	GetConversationAdminListReq struct {
		pagination.Pagination
		StartTime      time.Time      `form:"start_time" json:"start_time"`             // 时间开始
		EndTime        time.Time      `form:"end_time" json:"end_time"`                 // 时间结束
		DateType       enums.DateType `form:"date_type" json:"date_type"`               // 日期类型 1:创建时间,2:最近消息时间
		ID             string         `form:"id" json:"id"`                             // 会话记录ID
		Keyword        string         `form:"keyword" json:"keyword"`                   // 关键字（搜索消息内容）
		PostID         string         `form:"post_id" json:"post_id"`                   // 帖子ID
		MerchantUserID string         `form:"merchant_user_id" json:"merchant_user_id"` // 商家用户ID
		ClientUserID   string         `form:"client_user_id" json:"client_user_id"`     // 客户用户ID
	}

	// GetConversationAdminListData 管理端会话列表数据
	GetConversationAdminListData struct {
		ID               string     `json:"id"`                 // 会话记录ID
		ClientUserInfo   UserInfo   `json:"client_user_info"`   // 客户用户信息
		MerchantUserInfo UserInfo   `json:"merchant_user_info"` // 商家用户信息
		MessageCount     int        `json:"message_count"`      // 消息数量
		LastMessageTime  *time.Time `json:"last_message_time"`  // 最后消息时间
		CreatedAt        time.Time  `json:"created_at"`         // 创建时间
	}

	// GetConversationAdminListResp 获取管理端会话列表响应
	GetConversationAdminListResp struct {
		List  []*GetConversationAdminListData `json:"list"`  // 会话列表
		Total int64                           `json:"total"` // 总数
	}
)

// 管理端会话详情相关结构体
type (
	// GetConversationAdminDetailReq 获取管理端会话详情请求
	GetConversationAdminDetailReq struct {
		ID string `form:"id" json:"id" binding:"required"` // 会话记录ID
	}

	// GetConversationAdminDetailResp 获取管理端会话详情响应
	GetConversationAdminDetailResp struct {
		ID               string    `json:"id"`                 // 会话记录ID
		ClientUserInfo   UserInfo  `json:"client_user_info"`   // 客户用户信息
		MerchantUserInfo UserInfo  `json:"merchant_user_info"` // 商家用户信息
		CreatedAt        time.Time `json:"created_at"`         // 创建时间
		UpdatedAt        time.Time `json:"updated_at"`         // 更新时间
	}
)
