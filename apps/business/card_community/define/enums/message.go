package enums

// MessageType 消息类型
type MessageType int

const (
	MessageTypeText  MessageType = 1 // 文本
	MessageTypeImage MessageType = 2 // 图片
	MessageTypePost  MessageType = 3 // 帖子快照
)

// String 返回消息类型的字符串表示
func (t MessageType) String() string {
	switch t {
	case MessageTypeText:
		return "文本"
	case MessageTypeImage:
		return "图片"
	case MessageTypePost:
		return "帖子快照"
	default:
		return "未知类型"
	}
}

// IsValid 检查消息类型是否有效
func (t MessageType) IsValid() bool {
	switch t {
	case MessageTypeText, MessageTypeImage, MessageTypePost:
		return true
	default:
		return false
	}
}

// MessageDirection 消息方向
type MessageDirection int

const (
	MessageDirectionBigToSmall MessageDirection = -1 // 较大uid向较小uid发送消息
	MessageDirectionSmallToBig MessageDirection = 1  // 较小uid向较大uid发送消息
)

// Int32 返回消息方向的int32值
func (d MessageDirection) Int32() int32 {
	return int32(d)
}

// SenderType 发送者类型
type SenderType int

const (
	SenderTypeUser     SenderType = 1 // 用户
	SenderTypeMerchant SenderType = 2 // 商家
	SenderTypeSystem   SenderType = 3 // 系统
)

// String 返回发送者类型的字符串表示
func (t SenderType) String() string {
	switch t {
	case SenderTypeUser:
		return "用户"
	case SenderTypeMerchant:
		return "商家"
	case SenderTypeSystem:
		return "系统"
	default:
		return "未知类型"
	}
}

// IsValid 检查发送者类型是否有效
func (t SenderType) IsValid() bool {
	switch t {
	case SenderTypeUser, SenderTypeMerchant, SenderTypeSystem:
		return true
	default:
		return false
	}
}
