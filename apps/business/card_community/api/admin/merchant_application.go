package admin

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetMerchantApplicationListForAdmin
// @Summary 获取商家申请列表（管理端）
// @Description 管理员查看商家申请列表，支持多条件筛选
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/商家管理"
// @Param data query define.GetMerchantApplicationAdminListReq true "查询参数"
// @Success 200 {object} define.GetMerchantApplicationAdminListResp
// @Router /admin/v1/merchant_applications/list [get]
func GetMerchantApplicationListForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMerchantApplicationAdminListReq{}, s.GetMerchantApplicationListForAdmin)
}

// ReviewMerchantApplication
// @Summary 审核商家申请（管理端）
// @Description 管理员审核商家申请，通过或拒绝
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/商家管理"
// @Param data body define.ReviewMerchantApplicationReq true "审核参数"
// @Success 200 {object} define.ReviewMerchantApplicationResp
// @Router /admin/v1/merchant_applications/review [post]
func ReviewMerchantApplication(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ReviewMerchantApplicationReq{}, s.ReviewMerchantApplication)
}
