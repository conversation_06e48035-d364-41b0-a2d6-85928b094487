package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// CreatePost
// @Summary 创建帖子
// @Description 商家发布求购帖子
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data body define.CreatePostReq true "创建帖子参数"
// @Success 200 {object} define.CreatePostResp
// @Router /web/v1/posts/add [post]
func CreatePost(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CreatePostReq{}, s.CreatePost)
}

// GetPostList
// @Summary 获取帖子列表
// @Description 获取求购帖子列表，支持关键字搜索和价格筛选
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data query define.GetPostListReq true "查询参数"
// @Success 200 {object} define.GetPostListResp
// @Router /web/v1/posts/list [get]
func GetPostList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPostListReq{}, s.GetPostList)
}

// GetPostDetail
// @Summary 获取帖子详情
// @Description 获取指定帖子的详细信息
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data query define.GetPostDetailReq true "查询参数"
// @Success 200 {object} define.GetPostDetailResp
// @Router /web/v1/posts/detail [get]
func GetPostDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPostDetailReq{}, s.GetPostDetail)
}

// GetMyPosts
// @Summary 获取我的帖子
// @Description 获取当前商家发布的帖子列表
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data query define.GetMyPostListReq true "查询参数"
// @Success 200 {object} define.GetMyPostListResp
// @Router /web/v1/posts/my [get]
func GetMyPosts(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMyPostListReq{}, s.GetMyPosts)
}

// EditPost
// @Summary 编辑帖子内容
// @Description 商家编辑自己帖子的内容（描述、价格、媒体文件）。
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data body define.EditPostReq true "编辑参数"
// @Success 200 {object} define.EditPostResp
// @Router /web/v1/posts/edit [post]
func EditPost(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditPostReq{}, s.EditPost)
}

// UpdatePostStatus
// @Summary 更新帖子状态
// @Description 商家更新自己帖子的状态（上架/下架）
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data body define.UpdatePostStatusReq true "更新参数"
// @Success 200 {object} define.UpdatePostStatusResp
// @Router /web/v1/posts/edit_status [post]
func UpdatePostStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdatePostStatusReq{}, s.UpdatePostStatus)
}

// DeletePost
// @Summary 删除帖子
// @Description 商家删除自己的帖子
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/帖子管理"
// @Param data body define.DeletePostReq true "删除参数"
// @Success 200 {object} define.DeletePostResp
// @Router /web/v1/posts/delete [post]
func DeletePost(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DeletePostReq{}, s.DeletePost)
}
