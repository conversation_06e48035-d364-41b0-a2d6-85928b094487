package logic

import (
	"context"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	commondefine "app_service/apps/platform/common/define"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// ValidateConversationAccess 验证会话是否属于当前用户
// 返回会话信息和错误
func ValidateConversationAccess(ctx context.Context, conversationID, userID string) (*model.Conversation, error) {
	conversationSchema := repo.GetQuery().Conversation
	conversation, err := repo.NewConversationRepo(conversationSchema.WithContext(ctx)).SelectOne(
		search.NewQueryBuilder().
			Eq(conversationSchema.ID, conversationID).
			Eq(conversationSchema.ParticipantID, userID).
			Eq(conversationSchema.IsDeleted, int32(0)).
			Build(),
	)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500101Err
		}
		log.Ctx(ctx).Errorf("查询会话失败: %v", err)
		return nil, commondefine.CommonErr
	}
	return conversation, nil
}

// MarkConversationAsRead 标记会话为已读
// 更新指定会话的 LastReadTime 为当前时间，UnreadCount 重置为 0
// 注意：调用前需要先通过 ValidateConversationAccess 验证权限
func MarkConversationAsRead(ctx context.Context, conversationID string) error {
	// 获取会话表schema
	conversationSchema := repo.GetQuery().Conversation

	now := time.Now()
	updateData := map[string]interface{}{
		"last_read_time": &now,
		"unread_count":   0,
		"updated_at":     now,
	}

	err := repo.NewConversationRepo(conversationSchema.WithContext(ctx)).UpdateField(
		updateData,
		search.NewQueryBuilder().
			Eq(conversationSchema.ID, conversationID).
			Build(),
	)
	if err != nil {
		log.Ctx(ctx).Errorf("标记会话已读失败: conversationID=%s, error=%v", conversationID, err)
		return err
	}

	log.Ctx(ctx).Infof("会话已标记为已读: conversationID=%s", conversationID)
	return nil
}
