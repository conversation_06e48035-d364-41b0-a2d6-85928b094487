package logic

import (
	"app_service/apps/business/trade/dal/model"
	"app_service/apps/business/trade/repo"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"github.com/shopspring/decimal"
)

func UpdateMarketOverview(ctx context.Context) error {
	logPrefix := "UpdateMarketOverview"
	isHoliday, err := tmt.IsHoliday(ctx)
	if err != nil {
		return err
	}
	// 假期不更新
	if isHoliday {
		log.Ctx(ctx).Infof("%s today is holiday, skip.", logPrefix)
		return nil
	}
	// 未开市不更新
	//allowSale, err := tmt.AllowSale(ctx)
	//if err != nil {
	//	return err
	//}
	//if !allowSale {
	//	log.Ctx(ctx).Infof("%s not allow sale, skip.", logPrefix)
	//	return nil
	//}

	// 更新当天数据
	err = UpdateTodayOverview(ctx)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		log.Ctx(ctx).Errorf("%s UpdateTodayOverview error: %v", logPrefix, err)
	}
	// 更新历史累计数据
	err = UpdateTotalOverview(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("%s UpdateTotalOverview error: %v", logPrefix, err)
	}

	return nil
}

// UpdateTodayOverview 更新交易日当天的概览数据
func UpdateTodayOverview(ctx context.Context) error {
	now := util.Now()
	todayBegin := util.GetStartOfDay(now)
	todayEnd := util.GetEndOfDay(now)
	// 获取当天二手商品的成交额
	transAmountMap, err := issueFacade.GetTransactionAmountMapByTime(ctx, todayBegin, todayEnd)
	if err != nil {
		return err
	}
	// 获取当天一手商品的成交额
	issueItemTransAmountMap, err := issueFacade.GetIssueItemTransAmountMapByTime(ctx, todayBegin, todayEnd)
	if err != nil {
		return err
	}
	var tradeItemIDs []string
	var todayTransactionAmount int64
	// 二手
	for itemID, amount := range transAmountMap {
		if amount > 0 && !util.In(itemID, tradeItemIDs) {
			tradeItemIDs = append(tradeItemIDs, itemID)
		}
		todayTransactionAmount += amount
	}
	// 一手
	for itemID, amount := range issueItemTransAmountMap {
		if amount > 0 && !util.In(itemID, tradeItemIDs) {
			tradeItemIDs = append(tradeItemIDs, itemID)
		}
		todayTransactionAmount += amount
	}

	// 计算涨、跌、平
	var upCount, downCount, flatCount int64
	if len(tradeItemIDs) > 0 {
		// 获取商品的涨跌幅
		priceChangeRateMap, err := GetPriceChangeRateMapByItemIDs(ctx, tradeItemIDs)
		if err != nil {
			return err
		}
		for _, priceChangeRate := range priceChangeRateMap {
			if priceChangeRate > 0 {
				upCount++
			} else if priceChangeRate < 0 {
				downCount++
			} else if priceChangeRate == 0 {
				flatCount++
			}
		}
	}

	moSchema := repo.GetQuery().MarketOverview
	qw := search.NewQueryBuilder().Eq(moSchema.TradingDay, todayBegin).Build()
	moList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(ctx).Limit(1)).SelectList(qw)
	if err != nil {
		return err
	}
	if len(moList) == 0 {
		// 新增
		m := &model.MarketOverview{
			ID:                snowflakeutl.GenerateID(),
			TransactionAmount: todayTransactionAmount,
			UpCount:           upCount,
			DownCount:         downCount,
			FlatCount:         flatCount,
			TradingDay:        todayBegin,
		}
		return repo.NewMarketOverviewRepo(moSchema.WithContext(ctx)).Save(m)
	} else {
		marketOverview := moList[0]
		// 更新
		updateParams := map[string]interface{}{
			"transaction_amount": todayTransactionAmount,
			"up_count":           upCount,
			"down_count":         downCount,
			"flat_count":         flatCount,
		}
		updateQw := search.NewQueryBuilder().Eq(moSchema.ID, marketOverview.ID).Build()
		return repo.NewMarketOverviewRepo(moSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
	}
}

// UpdateTotalOverview 更新历史累计的概览数据
func UpdateTotalOverview(ctx context.Context) error {
	logPrefix := "UpdateTotalOverview"
	// 获取有效的流通商品
	itemIDs, err := issueFacade.GetAllValidIssueItemIDs(ctx)
	if err != nil {
		return err
	}
	if len(itemIDs) == 0 {
		log.Ctx(ctx).Infof("%s itemIDs is empty, skip.", logPrefix)
		return nil
	}
	issueItemMap, err := issueFacade.GetIssueItemMap(ctx, itemIDs)
	if err != nil {
		return err
	}
	// 获取商品的持仓总数
	itemCountMap, err := GetTotalCirculationMapFromYc(ctx, itemIDs)
	if err != nil {
		return err
	}
	// 最新收盘价
	closePriceMap, err := issueFacade.GetLatestClosePriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}
	// 计算总市值
	var totalMarketAmount int64
	nowTime := util.Now()
	for _, itemID := range itemIDs {
		closePrice := closePriceMap[itemID]
		totalCirculation := itemCountMap[itemID]
		if issueItem, ok := issueItemMap[itemID]; ok {
			// 允许流通，且已经开始流通了才计算累计市值
			if issueItem.CirculationStatus == mongdb.CirculationStatusAllow && issueItem.CirculationStart != nil &&
				issueItem.CirculationStart.Before(nowTime) {
				if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(nowTime) {
					totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
				}

				log.Ctx(ctx).Debugf("%s item: %s, totalCirculation: %v, closePrice: %v", logPrefix, itemID, totalCirculation, closePrice)
				if totalCirculation > 0 && closePrice > 0 {
					totalMarketAmount += totalCirculation * int64(closePrice)
				}
			}
		}
	}

	// 历史累计成交额
	transAmountMap, err := issueFacade.GetTotalTransactionAmountMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}
	var totalTransAmount int64
	for _, amount := range transAmountMap {
		totalTransAmount += amount
	}
	// 成交额百分比
	transAmountRate, err := CalculateTransAmountRate(ctx)
	if err != nil {
		return err
	}

	// 历史累计市值百分比计算
	// 上一个交易日的数据
	now := util.Now()
	todayStartTime := util.GetStartOfDay(now)
	moSchema := repo.GetQuery().MarketOverview
	preQw := search.NewQueryBuilder().
		Lt(moSchema.TradingDay, todayStartTime).
		OrderByDesc(moSchema.TradingDay).
		Build()
	moList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(ctx).Limit(1)).SelectList(preQw)
	if err != nil {
		return err
	}
	// 计算市值百分比
	// （当天市值-上个交易日市值）/上个交易日市值*100%，四舍五入保留小数点后 2 位。
	var marketAmountRate float32
	minRate := decimal.NewFromFloat32(0.01)
	if len(moList) > 0 && moList[0].TotalMarketAmount > 0 {
		preMarketOverview := moList[0]
		diff := decimal.NewFromInt(totalMarketAmount).Sub(decimal.NewFromInt(preMarketOverview.TotalMarketAmount))
		rate := diff.Div(decimal.NewFromInt(preMarketOverview.TotalMarketAmount)).Mul(decimal.NewFromInt(100))
		if rate.GreaterThan(decimal.Zero) && rate.LessThan(minRate) {
			rate = minRate
		}

		r, _ := rate.Round(2).Float64()
		marketAmountRate = float32(r)
	}

	todayQw := search.NewQueryBuilder().
		Eq(moSchema.TradingDay, todayStartTime).
		Build()
	todayMoList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(ctx).Limit(1)).SelectList(todayQw)
	if err != nil {
		return err
	}

	if len(todayMoList) == 0 {
		// 新增
		m := &model.MarketOverview{
			ID:                     snowflakeutl.GenerateID(),
			TradingDay:             todayStartTime,
			TotalTransactionAmount: totalTransAmount,
			TransactionAmountRate:  transAmountRate,
			TotalMarketAmount:      totalMarketAmount,
			MarketAmountRate:       marketAmountRate,
		}
		return repo.NewMarketOverviewRepo(moSchema.WithContext(ctx)).Save(m)
	} else {
		todayMarketOverview := todayMoList[0]
		// 更新
		updateParams := map[string]interface{}{
			"total_transaction_amount": totalTransAmount,
			"transaction_amount_rate":  transAmountRate,
			"total_market_amount":      totalMarketAmount,
			"market_amount_rate":       marketAmountRate,
		}
		updateQw := search.NewQueryBuilder().Eq(moSchema.ID, todayMarketOverview.ID).Build()
		return repo.NewMarketOverviewRepo(moSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
	}
}

// CalculateTransAmountRate 计算成交额百分比
// 当天成交额/上个交易日成交额*100%，四舍五入保留小数点后 2 位。
// 当天无成交的保留显示上个交易日的数据。
func CalculateTransAmountRate(ctx context.Context) (float32, error) {
	now := util.Now()
	todayStartTime := util.GetStartOfDay(now)
	todayEndTime := util.GetEndOfDay(now)
	// 当天成交额
	todayTransAmountMap, err := issueFacade.GetTransactionAmountMapByTime(ctx, todayStartTime, todayEndTime)
	if err != nil {
		return 0, err
	}
	var todayTotalTransAmount int64
	for _, amount := range todayTransAmountMap {
		todayTotalTransAmount += amount
	}
	// 上一个交易日的数据
	moSchema := repo.GetQuery().MarketOverview
	qw := search.NewQueryBuilder().
		Select(moSchema.TradingDay, moSchema.TransactionAmountRate).
		Lt(moSchema.TradingDay, todayStartTime).
		OrderByDesc(moSchema.TradingDay).
		Build()
	moList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(ctx).Limit(1)).SelectList(qw)
	if err != nil {
		return 0, err
	}
	var mo *model.MarketOverview
	if len(moList) > 0 {
		mo = moList[0]
	}
	if todayTotalTransAmount == 0 {
		// 取上一个交易日的百分比
		if mo != nil {
			return mo.TransactionAmountRate, nil
		}
		// 上一个交易日没有数据，返回 0
		return 0, nil
	}

	// 上一个交易日的成交额
	var preTotalTransAmount int64
	if mo != nil {
		preStartTime := util.GetStartOfDay(mo.TradingDay)
		preEndTime := util.GetEndOfDay(mo.TradingDay)
		preTransAmountMap, err := issueFacade.GetTransactionAmountMapByTime(ctx, preStartTime, preEndTime)
		if err != nil {
			return 0, err
		}
		for _, amount := range preTransAmountMap {
			preTotalTransAmount += amount
		}
	}
	if preTotalTransAmount == 0 {
		preTotalTransAmount = 100 // 单位是：分
	}

	rate := decimal.NewFromInt(todayTotalTransAmount).
		Div(decimal.NewFromInt(preTotalTransAmount)).
		Mul(decimal.NewFromInt(100))
	minRate := decimal.NewFromFloat32(0.01)
	if rate.GreaterThan(decimal.Zero) && rate.LessThan(minRate) {
		rate = minRate
	}
	r, _ := rate.Round(2).Float64()

	return float32(r), nil
}
