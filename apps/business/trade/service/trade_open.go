package service

import (
	"app_service/apps/business/trade/define"
	"app_service/apps/business/trade/repo"
	"app_service/apps/business/trade/service/logic"
	"app_service/pkg/search"
	"app_service/pkg/util"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"github.com/go-redis/redis/v8"
	"time"
)

func (s *Service) SyncCirculationItem(req *define.SyncCirculationItemReq) (*define.SyncCirculationItemResp, error) {
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 异步更新数据
		startTime := util.Now()
		err := logic.SyncCirculationItem(spanCtx)
		if err != nil {
			log.Ctx(spanCtx).Errorf("sync circulation item err:%v", err)
			return
		}
		log.Ctx(spanCtx).Infof("sync circulation item success! duration: %v", time.Since(startTime))
	}()

	return &define.SyncCirculationItemResp{}, nil
}

func (s *Service) UpdateMarketOverview(req *define.UpdateMarketOverviewReq) (*define.UpdateMarketOverviewResp, error) {
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 异步更新数据
		startTime := util.Now()
		err := logic.UpdateMarketOverview(spanCtx)
		if err != nil {
			log.Ctx(spanCtx).Errorf("update market overview err:%v", err)
			return
		}
		log.Ctx(spanCtx).Infof("update market overview success! duration: %v", time.Since(startTime))
	}()

	return &define.UpdateMarketOverviewResp{}, nil
}

func (s *Service) BatchUpdateCirculationItem(req *define.BatchUpdateCirculationItemReq) (*define.BatchUpdateCirculationItemResp, error) {
	logPrefix := "BatchUpdateCirculationItem"
	for {
		itemIDs, err := logic.GetFromCirculationItemUpdateQueue(s.ctx, 10)
		if err != nil && !errors.Is(err, redis.Nil) {
			return nil, err
		}

		log.Ctx(s.ctx).Debugf("%s itemIDs: %v", logPrefix, itemIDs)

		if errors.Is(err, redis.Nil) || len(itemIDs) == 0 {
			log.Ctx(s.ctx).Infof("%s no need to update", logPrefix)
			break
		}

		// 更新涨跌幅
		for _, itemID := range itemIDs {
			priceChangeRate, err := logic.GetPriceChangeRateFromCache(s.ctx, itemID)
			if err != nil {
				log.Ctx(s.ctx).Errorf("%s redis zscore err:%v", logPrefix, err)
				continue
			}
			ciSchema := repo.GetQuery().CirculationItem
			UpdateQw := search.NewQueryBuilder().Eq(ciSchema.ItemID, itemID).Build()
			updateParams := map[string]interface{}{
				"price_change_rate": float32(priceChangeRate),
			}
			err = repo.NewCirculationItemRepo(ciSchema.WithContext(s.ctx)).UpdateField(updateParams, UpdateQw)
			if err != nil && !errors.Is(err, repo.UpdateFail) {
				log.Ctx(s.ctx).Errorf("%s update circulation_item err:%v", logPrefix, err)
				continue
			}
		}
	}

	return &define.BatchUpdateCirculationItemResp{}, nil
}
