package enums

type CirculationItemListTypeEnum string

func (r CirculationItemListTypeEnum) Val() string {
	return string(r)
}

const (
	CirculationItemListTypeHot               CirculationItemListTypeEnum = "hot"                // 热榜
	CirculationItemListTypeTransactionAmount CirculationItemListTypeEnum = "transaction_amount" // 成交额榜
	CirculationItemListTypeMarketAmount      CirculationItemListTypeEnum = "market_amount"      // 市值榜
	CirculationItemListTypeAll               CirculationItemListTypeEnum = "all"                // 全部（二手默认）

	CirculationItemOrderByTransactionAmount = "transaction_amount" // 成交额
	CirculationItemOrderByMarketAmount      = "market_amount"      // 市值
	CirculationItemOrderByPriceChangeRate   = "price_change_rate"  // 涨跌幅
)

type CirculationItemDisplayEnum int32

func (r CirculationItemDisplayEnum) Val() int32 {
	return int32(r)
}

const (
	CirculationItemDisplayYes CirculationItemDisplayEnum = 1
	CirculationItemDisplayNo  CirculationItemDisplayEnum = 2
)
