package router

import (
	"app_service/apps/business/trade/router/open"
	"fmt"

	"app_service/apps/business/trade/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"

	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 客户端路由
	webRoute(r)
	// 开放路由
	openRoute(r)
}

// 客户端路由
func webRoute(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.<PERSON>(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/trade/circulation_item/list",
			"/web/v1/trade/issue_item/top_list",
			"/web/v1/trade/circulation_item/top_list",
			"/web/v1/trade/market/overview",
			"/web/v1/trade/circulation_item/overview",
			"/web/v1/trade/activities",
		},
	}))
	web.Trade(w)
}

// 开放路由
func openRoute(router *gin.Engine) {
	w := router.Group("/open/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
	}))
	open.Trade(w)
}
