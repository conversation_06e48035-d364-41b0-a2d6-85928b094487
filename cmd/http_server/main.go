package main

import (
	"context"
	tracerProvider "e.coding.net/g-dtay0385/common/opentelemetry-tracer-provider"
	"fmt"
	"github.com/chenmingyong0423/go-mongox/v2"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"go.mongodb.org/mongo-driver/v2/mongo/readpref"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/propagation"
	"os"
	"strings"
	"time"

	"app_service/apps"
	invite_reward_consume "app_service/apps/business/invite_reward/consume"
	story_consume "app_service/apps/business/story/consume"
	synthesis_consume "app_service/apps/business/synthesis/consume"
	tradeconsume "app_service/apps/business/trade/consume"
	shipment_consume "app_service/apps/business/yc/consume"
	"app_service/apps/platform/user/consume"
	_ "app_service/docs"
	"app_service/global"
	"app_service/pkg/util/snowflakeutl"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	"e.coding.net/g-dtay0385/common/go-initialize"
	"e.coding.net/g-dtay0385/common/go-kafka/manager"
	"e.coding.net/g-dtay0385/common/go-kafka/subscriber"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-redis"
	"github.com/go-micro/plugins/v4/server/http"
	"github.com/go-micro/plugins/v4/wrapper/trace/opentelemetry"
	"github.com/urfave/cli/v2"
	"go-micro.dev/v4"
	"go-micro.dev/v4/server"

	"github.com/calvinit/jiguang-sdk-go/api/jpush/push"
)

//go:generate swag init -d ../../apps  -g ../cmd/http_server/main.go -o ../../docs --instanceName app_service --parseDependency --parseDepth 6  --parseInternal

// @title app API
// @version 1.0.0
// @description 接口文档

// @securityDefinitions.apikey Bearer
// @in header
// @name Authorization
func main() {
	// 获取服务实例
	s := GetService()

	//初始化时区
	location, _ := time.LoadLocation("Asia/Shanghai")
	time.Local = location

	s.Init()
	err := s.Run()
	if err != nil {
		panic(fmt.Sprintf("服务启动失败 err:%+v", err))
	}
}

// GetService 获取服务
func GetService() micro.Service {
	// 初始化组件
	initComponents()

	// 初始化雪花id生成器
	snowflakeutl.Init(global.REDIS.Client)

	// 构建服务
	return buildService()
}

// 初始化组件
func initComponents() {
	// 加载配置
	options := make([]initialize.Option, 0)
	env := strings.ToLower(os.Getenv("ENV"))
	if env == global.EnvDev {
		options = append(options, initialize.WithConfigFile("./conf/http_server/config-dev.yaml"))
	}
	initializer := initialize.New(global.GlobalConfig, options...)

	// 初始化组件
	initializer.InitComponents(
		// 日志
		initialize.WithLoggerConfig(global.GlobalConfig.Logger),
		// mysql
		initialize.WithMysqlConfig(initialize.MysqlConfig{Mysql: global.GlobalConfig.Mysql}),
		// redis
		initialize.WithRedisConfig(initialize.RedisConfig{Redis: global.GlobalConfig.Redis}),
		// kafka
		initialize.WithKafkaBrokersConfig(global.GlobalConfig.Kafka),
	)
	//初始化Global
	initGlobal()

	//初始化Kafka
	initKafkaConsumer()

	//初始化mongo
	initMongoDB()

	// 初始化 tracer
	initTracer()

	// 初始化极光推送
	//initJPush()
}

// 构建http服务器
func buildHttpServer() server.Server {
	// 初始化路由
	r := apps.Init()
	// 加载路由
	for _, f := range global.Routers {
		f()
	}

	// 构建http服务器
	srv := http.NewServer(
		server.Address(global.GlobalConfig.Service.Address),
		server.Name(global.GlobalConfig.Service.Name),
		server.Version(global.GlobalConfig.Service.Version),
		server.WithLogger(log.Logger),
	)

	// 绑定路由
	hd := server.NewHandler(r)
	err := srv.Handle(hd)
	if err != nil {
		panic(fmt.Sprintf("路由绑定失败 err:%+v", err))
	}

	// 初始化主站请求
	request.InitNodeHttpConfigs(global.GlobalConfig.MasterHttp)

	return srv
}

// 构建服务
func buildService() micro.Service {
	return micro.NewService(
		micro.Name(global.GlobalConfig.Service.Name),
		micro.Server(buildHttpServer()),
		micro.WrapHandler(opentelemetry.NewHandlerWrapper()),
		micro.WrapCall(opentelemetry.NewCallWrapper()),
		micro.Flags(&cli.StringFlag{Name: "confFile"}),
	)
}

// 初始化 Global
func initGlobal() {
	//Redis
	global.REDIS = redis.GetClient("app_service")

	//Kafka
	global.BROKER = manager.GetBroker(global.GlobalConfig.Kafka[0].Name)
}

func initKafkaConsumer() {
	//注册消费者
	consumers := []subscriber.Consumer{
		consume.NewUserUpdateConsumer(),
		consume.NewUserPurchaseConsumer(),
		consume.NewUserRealAuthConsumer(),
		consume.NewUserLoginConsumer(),
		invite_reward_consume.NewUserBindConsumer(),
		synthesis_consume.NewSynthesisFusionConsumer(),
		synthesis_consume.NewSynthesisCancelFusionConsumer(),
		synthesis_consume.NewPriorityBuyIssueConsumer(),
		synthesis_consume.NewSynthesisItemIssueConsumer(),
		story_consume.NewStoryFusionConsumer(),
		//story_consume.NewStoryCancelFusionConsumer(),
		story_consume.NewStoryItemIssueConsumer(),
		shipment_consume.NewShipmentForceConsumer(),
		tradeconsume.NewUserPurchaseConsumer(),
		tradeconsume.NewCirculationControlConsumer(),
		tradeconsume.NewIssueItemShelvesConsumer(),
	}

	// 通过代理节点初始化订阅者
	err := subscriber.InitWithBroker(manager.GetBroker(global.GlobalConfig.Kafka[0].Name), consumers...)
	if err != nil {
		return
	}
}

func initMongoDB() {
	uri := fmt.Sprintf("mongodb://%s", global.GlobalConfig.Mongo.Uri)
	clientOptions := options.Client().ApplyURI(uri).
		SetAuth(options.Credential{
			Username:   global.GlobalConfig.Mongo.User,
			Password:   global.GlobalConfig.Mongo.Password,
			AuthSource: global.GlobalConfig.Mongo.Database,
		})
	// 连接到 MongoDB
	mongoClient, err := mongo.Connect(clientOptions)
	if err != nil {
		log.Fatal(err)
		panic("connect mongodb error")
	}

	// 检查连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	err = mongoClient.Ping(ctx, readpref.Primary())
	if err != nil {
		log.Fatal(err)
		panic("connect mongodb error")
	}

	// 关闭连接
	//err = mongoClient.Disconnect(context.TODO())
	//if err != nil {
	//	panic("disconnect from mongodb fail")
	//}

	// 初始化全局MongoDB数据库连接映射
	if global.GlobalConfig.Service.Env == global.EnvProd {
		global.MongoDBList = map[string]*mongox.Database{
			global.TmtDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.TmtDb + "-" + global.EnvProd),
			global.WatDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.WatDb + "-" + global.EnvProd),
			global.PatDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.PatDb + "-" + global.EnvProd),
			global.CogDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.CogDb + "-" + global.EnvProd),
		}
	} else {
		global.MongoDBList = map[string]*mongox.Database{
			global.TmtDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.TmtDb + "-sit"),
			global.WatDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.WatDb + "-sit"),
			global.PatDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.PatDb + "-sit"),
			global.CogDb: mongox.NewClient(mongoClient, &mongox.Config{}).NewDatabase(global.CogDb + "-sit"),
		}
	}

}

func initTracer() {
	if global.GlobalConfig.Service.Env == global.EnvDev {
		return
	}
	logPrefix := "[initTracer]"
	tp, err := tracerProvider.BuildTraceProvider(global.GlobalConfig.Tracer)
	if err != nil {
		log.Fatal(logPrefix+" tracerProvider.BuildTraceProvider error:", err)
		return
	}
	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagation.TraceContext{}, propagation.Baggage{}))
}

func initJPush() {
	pushAPIv3, err := push.NewAPIv3Builder().
		SetAppKey(global.GlobalConfig.JPush.AppKey).
		SetMasterSecret(global.GlobalConfig.JPush.MasterSecret).
		Build()
	if err != nil {
		log.Fatal("初始化极光推送失败：", err)
		return
	}
	global.JPushAPIv3 = pushAPIv3
}
