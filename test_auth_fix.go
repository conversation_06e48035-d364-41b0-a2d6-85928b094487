package main

import (
	"context"
	"fmt"
	"time"

	"app_service/global"
	"app_service/pkg/middlewares/g/auth"
)

func main() {
	fmt.Println("=== 认证修复测试 ===")

	// 跳过日志初始化，直接测试

	// 模拟配置
	global.GlobalConfig = &global.Config{
		Auth: &global.AuthConf{
			RemoteTimeout:  10, // 10秒超时
			EnableFallback: false,
			PublicKey:      "",
		},
	}

	// 创建认证实例
	webAuth := &auth.Web{
		NoAuthUrl:     []string{},
		RemoteTimeout: global.GlobalConfig.Auth.RemoteTimeout,
	}

	// 测试token
	token := "************************************************************************************************************************************************************************************************************************************************************************************************************************************"

	ctx := context.Background()

	fmt.Printf("配置的超时时间: %d秒\n", webAuth.RemoteTimeout)
	fmt.Printf("测试Token: %s...\n", token[:50])
	fmt.Println()

	// 测试1: 正常超时时间
	fmt.Println("测试1: 使用10秒超时...")
	start := time.Now()
	info, err := webAuth.CheckToken(token, ctx)
	duration := time.Since(start)

	fmt.Printf("耗时: %v\n", duration)
	if err != nil {
		fmt.Printf("❌ 认证失败: %v\n", err)
	} else {
		fmt.Printf("✅ 认证成功: %+v\n", info)
	}
	fmt.Println()

	// 测试2: 短超时时间
	fmt.Println("测试2: 使用1秒超时...")
	webAuth.RemoteTimeout = 1
	start = time.Now()
	info, err = webAuth.CheckToken(token, ctx)
	duration = time.Since(start)

	fmt.Printf("耗时: %v\n", duration)
	if err != nil {
		fmt.Printf("❌ 认证失败: %v\n", err)
	} else {
		fmt.Printf("✅ 认证成功: %+v\n", info)
	}

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("修复说明:")
	fmt.Println("1. 增加了可配置的超时时间")
	fmt.Println("2. 添加了详细的日志记录")
	fmt.Println("3. 解决了原始的超时问题")
}
