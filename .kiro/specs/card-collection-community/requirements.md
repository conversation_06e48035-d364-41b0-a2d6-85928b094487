# 需求文档

## 介绍

卡牌集社是一个专门为卡牌收藏爱好者打造的社区交易平台功能。该功能允许商家发布求购帖子，与收藏用户进行一对一沟通，并通过智能回复系统提升交易效率。系统集成了帖子管理、IM聊天功能、智能回复管理等核心能力，为商家和收藏用户提供便捷的卡牌交易体验。

## 需求

### 需求 1

**用户故事：** 作为一个商家用户，我想要发布求购帖子，以便收藏用户能够看到我的收购需求并主动联系我

#### 验收标准

1. WHEN 商家点击"发帖子"按钮 THEN 系统 SHALL 显示帖子发布页面
2. WHEN 商家上传卡片图片 THEN 系统 SHALL 支持多张图片上传并显示预览
3. WHEN 商家输入价格信息 THEN 系统 SHALL 验证价格格式的有效性
4. WHEN 商家输入描述信息 THEN 系统 SHALL 限制描述长度不超过500字符
5. WHEN 商家点击"立即发布" THEN 系统 SHALL 创建帖子记录并返回发布成功状态
6. WHEN 帖子发布成功 THEN 系统 SHALL 在"我的发布"页面显示该帖子信息

### 需求 2

**用户故事：** 作为一个收藏用户，我想要查看商家的求购帖子并与商家进行沟通，以便出售我的卡牌

#### 验收标准

1. WHEN 用户查看求购帖子 THEN 系统 SHALL 显示完整的帖子详情包括图片、价格、描述
2. WHEN 用户点击"联系商家"按钮 THEN 系统 SHALL 创建与该商家的会话
3. WHEN 会话创建成功 THEN 系统 SHALL 自动跳转到聊天页面
4. IF 商家开启了智能回复 THEN 系统 SHALL 自动发送预设的回复消息
5. WHEN 用户发送消息 THEN 系统 SHALL 保存消息到数据库
6. WHEN 商家回复消息 THEN 系统 SHALL 保存消息到数据库

### 需求 3

**用户故事：** 作为一个商家用户，我想要设置智能回复模板，以便自动回复用户的咨询提高响应效率

#### 验收标准

1. WHEN 商家进入"我的发布"页面 THEN 系统 SHALL 显示智能回复开关状态
2. WHEN 商家点击智能回复开关 THEN 系统 SHALL 切换智能回复的启用/禁用状态
3. IF 智能回复未设置 THEN 系统 SHALL 显示"你还未设置智能回复消息"提示
4. WHEN 商家点击设置智能回复 THEN 系统 SHALL 显示回复模板编辑页面
5. WHEN 商家编辑回复模板 THEN 系统 SHALL 限制模板内容不超过200字符
6. WHEN 商家保存模板 THEN 系统 SHALL 更新智能回复模板并显示保存成功
7. WHEN 新会话创建且智能回复开启 THEN 系统 SHALL 自动发送智能回复消息

### 需求 4

**用户故事：** 作为一个用户，我想要查看会话列表和聊天记录，以便管理我的所有对话

#### 验收标准

1. WHEN 用户进入消息页面 THEN 系统 SHALL 通过HTTP GET请求获取会话列表
2. WHEN 显示会话列表 THEN 系统 SHALL 包含商家头像、名称、最后消息、时间戳
3. IF 会话有未读消息 THEN 系统 SHALL 根据最后阅读时间显示未读消息数量标识
4. WHEN 用户点击会话 THEN 系统 SHALL 进入聊天详情页面
5. WHEN 进入聊天页面 THEN 系统 SHALL 通过HTTP GET请求获取分页的消息历史记录
6. WHEN 用户查看聊天页面 THEN 系统 SHALL 更新该用户在此会话的最后阅读时间

### 需求 5

**用户故事：** 作为一个用户，我想要发送和接收消息，以便与商家进行沟通

#### 验收标准

1. WHEN 用户发送文本消息 THEN 系统 SHALL 通过HTTP POST保存消息到数据库
2. WHEN 用户发送图片消息 THEN 系统 SHALL 先上传图片再通过HTTP POST保存消息
3. WHEN 用户下拉刷新聊天页面 THEN 系统 SHALL 重新获取最新的消息记录
4. WHEN 用户上拉加载 THEN 系统 SHALL 获取更多历史消息记录
5. WHEN 消息发送成功 THEN 系统 SHALL 刷新聊天页面显示新消息
6. WHEN 消息发送失败 THEN 系统 SHALL 显示错误提示并允许重试

### 需求 6

**用户故事：** 作为一个商家，我想要管理我的帖子发布，以便控制帖子的上架和下架状态

#### 验收标准

1. WHEN 商家进入"我的发布"页面 THEN 系统 SHALL 显示所有发布的帖子信息
2. WHEN 显示帖子列表 THEN 系统 SHALL 区分"已上架"和"已下架"状态
3. WHEN 商家切换状态标签 THEN 系统 SHALL 筛选显示对应状态的帖子
4. WHEN 商家点击帖子项目 THEN 系统 SHALL 显示帖子详情和操作选项
5. WHEN 商家选择下架帖子 THEN 系统 SHALL 更新帖子状态为已下架
6. WHEN 商家选择重新上架 THEN 系统 SHALL 更新帖子状态为已上架
7. WHEN 帖子状态变更 THEN 系统 SHALL 同步更新相关会话的可见性

### 需求 7

**用户故事：** 作为系统管理员，我想要监控IM系统的运行状态，以便确保消息传递的可靠性

#### 验收标准

1. WHEN 消息发送失败 THEN 系统 SHALL 记录错误日志并标记消息状态
2. WHEN 推送服务异常 THEN 系统 SHALL 启用备用推送机制
3. WHEN 数据库连接异常 THEN 系统 SHALL 返回适当的错误提示
4. WHEN 系统负载过高 THEN 系统 SHALL 限制并发请求数量
5. WHEN 消息存储达到阈值 THEN 系统 SHALL 自动清理过期消息