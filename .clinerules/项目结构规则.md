# 项目结构规则

本项目采用分层架构，目录结构清晰，便于扩展和维护。主要目录说明如下：

- `cmd/`：服务入口目录，包含 http_server、task_server 等子目录。每个子目录下的 main.go 为对应服务的启动入口。
- `apps/`：业务代码目录，分为 business（业务模块）和 platform（平台模块）。每个模块下通常包含 api、define、router、service 等子目录，分别负责接口定义、数据结构、路由注册和业务逻辑实现。
- `pkg/`：通用工具包目录，包含 util（工具函数）、middlewares（中间件）、search（搜索相关）、pagination（分页相关）等子目录，供各业务模块复用。
- `conf/`：配置文件目录，按服务类型（如 http_server、task_server）划分子目录，支持多环境配置（如 config-dev.yaml、config-prod.yaml）。
- `global/`：全局变量、配置和初始化逻辑。
- `third_party/`：第三方依赖或集成代码，按功能模块划分子目录。
- `docs/`：项目文档和 API 文档（如 swagger 文件）。

## 业务模块结构示例

以 `apps/business/yc/` 为例，结构如下：

- `api/`：接口定义。
- `define/`：数据结构定义。
- `router/`：路由注册，包含 web 和 admin 子路由。
- `service/`：业务逻辑实现，logic 子目录下为具体业务逻辑。

## 约定

- 业务代码与通用工具解耦，复用性强。
- 配置文件按服务和环境分类，便于管理和部署。
- 路由、数据结构、业务逻辑分层明确，便于维护和扩展。

如需详细了解某一目录或文件，请参考源码或相关文档。
