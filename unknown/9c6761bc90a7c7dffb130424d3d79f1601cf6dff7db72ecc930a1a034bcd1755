package define

import (
	"app_service/apps/business/card_community/define/enums"
)

// MediaFile 媒体文件结构体
type MediaFile struct {
	URL          string          `json:"url" binding:"required"`    // 文件URL
	Type         enums.MediaType `json:"type" binding:"required"`   // 媒体类型：1=图片 2=视频
	Size         int64           `json:"size" binding:"required"`   // 文件大小(字节)
	Width        int             `json:"width" binding:"required"`  // 宽度(像素)
	Height       int             `json:"height" binding:"required"` // 高度(像素)
	Duration     int             `json:"duration"`                  // 视频时长(秒)，图片为0
	ThumbnailURL string          `json:"thumbnail_url"`             // 缩略图URL
}

// UserInfo 用户信息
type UserInfo struct {
	ID     string `json:"id"`     // 用户ID
	Name   string `json:"name"`   // 用户名称
	Avatar string `json:"avatar"` // 用户头像
}

// PostSnapshot 帖子快照信息
type PostSnapshot struct {
	Description string       `json:"description"` // 帖子文案描述
	Price       int64        `json:"price"`       // 收购价格
	MediaFiles  []*MediaFile `json:"media_files"` // 媒体文件数组
}
