-- 商家申请表唯一约束迁移脚本
-- 为merchant_applications表的user_id字段添加唯一索引，确保一个用户只能有一条申请记录

-- 1. 首先清理重复数据（保留最新的申请记录）
-- 删除重复的申请记录，只保留每个用户最新的一条记录
DELETE ma1 FROM merchant_applications ma1
INNER JOIN merchant_applications ma2 
WHERE ma1.user_id = ma2.user_id 
  AND ma1.applied_at < ma2.applied_at;

-- 2. 添加唯一索引约束
ALTER TABLE merchant_applications 
ADD UNIQUE KEY `uk_user_id` (`user_id`);

-- 验证唯一约束添加结果
-- SELECT user_id, COUNT(*) as count FROM merchant_applications GROUP BY user_id HAVING count > 1;
-- 上述查询应该返回空结果，表示没有重复的user_id

-- 验证索引创建结果
-- SHOW INDEX FROM merchant_applications WHERE Key_name = 'uk_user_id';
