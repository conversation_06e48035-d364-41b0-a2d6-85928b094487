-- 卡牌集社价格字段迁移脚本
-- 将posts表的price字段从decimal(10,2)改为bigint，以分为单位存储

-- 1. 备份当前数据（可选）
-- CREATE TABLE posts_backup AS SELECT * FROM posts;

-- 2. 添加新的价格字段（以分为单位）
ALTER TABLE posts ADD COLUMN price_cents BIGINT NOT NULL DEFAULT 0 COMMENT '收购价格(分)';

-- 3. 将现有价格数据转换为分（乘以100）
UPDATE posts SET price_cents = ROUND(price * 100);

-- 4. 删除旧的价格字段
ALTER TABLE posts DROP COLUMN price;

-- 5. 重命名新字段为price
ALTER TABLE posts CHANGE COLUMN price_cents price BIGINT NOT NULL COMMENT '收购价格(分)';

-- 验证数据迁移结果
-- SELECT id, price FROM posts LIMIT 10;
