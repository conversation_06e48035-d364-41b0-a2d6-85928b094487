package tool

import (
	"bytes"
	"fmt"
	"os"
	"text/template"
)

const templateCode = `// Code generated by app_service/gen/tool. DO NOT EDIT.

package repo

import (
	"app_service/apps/{{.Prefix}}/{{.AppName}}/dal/model"
	"app_service/apps/{{.Prefix}}/{{.AppName}}/dal/query"
	"app_service/pkg/search"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type {{.AliasName}}Repository struct {
	do query.{{.InterfaceName}}
}

func New{{.AliasName}}Repo(do query.{{.InterfaceName}}) *{{.AliasName}}Repository {
	return &{{.AliasName}}Repository{
		do: do,
	}
}

func (r *{{.AliasName}}Repository) SelectOne(queryWrapper *search.QueryWrapper) (*model.{{.ModelName}}, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *{{.AliasName}}Repository) SelectList(queryWrapper *search.QueryWrapper) ([]*model.{{.ModelName}}, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *{{.AliasName}}Repository) SelectPage(queryWrapper *search.QueryWrapper, pageIndex int, pageSize int) ([]*model.{{.ModelName}}, int64, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}
	records, count, err := r.do.FindByPage(search.Paginate(pageSize, pageIndex))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *{{.AliasName}}Repository) Count(queryWrapper *search.QueryWrapper) (int64, error) {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	count, err := r.do.Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *{{.AliasName}}Repository) Save(model *model.{{.ModelName}}) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *{{.AliasName}}Repository) BatchSave(models []*model.{{.ModelName}}, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *{{.AliasName}}Repository) UpdateById(model *model.{{.ModelName}}) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) Update(ms *model.{{.ModelName}}, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(
			search.MakeOpt(queryWrapper.ScopeOpts...),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) UpdateField(params interface{}, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *{{.AliasName}}Repository) RemoveByIds(ms ...*model.{{.ModelName}}) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
`

type TemplateData struct {
	Prefix        string
	AppName       string
	AliasName     string
	InterfaceName string
	ModelName     string
}

func GenRepo(prefix string, appName string, aliasName string, fileName string, interfaceName string, modelName string) {
	data := TemplateData{
		Prefix:        prefix,
		AppName:       appName,
		AliasName:     aliasName,
		InterfaceName: interfaceName,
		ModelName:     modelName,
	}
	tmpl, err := template.New("code").Parse(templateCode)
	if err != nil {
		panic(err)
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		panic(err)
	}

	// Write the generated code to a file
	err = os.WriteFile(fmt.Sprintf("./apps/%s/%s/repo/%s.gen.go", prefix, appName, fileName), buf.Bytes(), 0644)
	if err != nil {
		panic(err)
	}

	// Print the generated code to the console
	println(buf.String())
}
