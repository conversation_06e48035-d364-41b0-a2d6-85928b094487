package card_community

import (
	"app_service/gen/tool"

	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

// genModel 生成卡牌集社模块的数据模型
func genModel() {
	// 数据库连接配置
	dsn := "root:123456@tcp(localhost:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}

	// 配置GORM生成器
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/card_community/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	// 配置软删除字段
	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	// 配置数据类型映射
	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	// 生成帖子模型
	post := g.GenerateModelAs("posts", "Post")

	// 生成会话模型
	conversation := g.GenerateModelAs("conversations", "Conversation")

	// 生成智能回复模板模型
	smartReplyTemplate := g.GenerateModelAs("smart_reply_templates", "SmartReplyTemplate")

	// 生成商家申请模型
	merchantApplication := g.GenerateModelAs("merchant_applications", "MerchantApplication")

	// 生成消息模型
	message := g.GenerateModelAs("messages", "Message")

	// 应用所有模型
	applyBasic := make([]any, 0)
	applyBasic = append(applyBasic, post, conversation, smartReplyTemplate, merchantApplication, message)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

// Gen 生成卡牌集社模块的数据模型和仓储层代码
func Gen() {
	genModel()

	// 生成帖子仓储层代码
	tool.GenRepo("business", "card_community", "Post", "post", "IPostDo", "Post")

	// 生成会话仓储层代码
	tool.GenRepo("business", "card_community", "Conversation", "conversation", "IConversationDo", "Conversation")

	// 生成智能回复模板仓储层代码
	tool.GenRepo("business", "card_community", "SmartReplyTemplate", "smart_reply_template", "ISmartReplyTemplateDo", "SmartReplyTemplate")

	// 生成商家申请仓储层代码
	tool.GenRepo("business", "card_community", "MerchantApplication", "merchant_application", "IMerchantApplicationDo", "MerchantApplication")

	// 生成消息仓储层代码
	tool.GenRepo("business", "card_community", "Message", "message", "IMessageDo", "Message")
}
