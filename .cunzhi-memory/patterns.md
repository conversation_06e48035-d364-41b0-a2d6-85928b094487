# 常用模式和最佳实践

- 先添加代码再添加导入，防止导入被自动删除
- 帖子管理业务逻辑已完成重构：1. 主要业务逻辑在 service 层实现（post_web.go, post_admin.go）2. 复用逻辑在 logic 层（merchant_permission.go）3. 使用 UserInfo 结构体统一用户信息格式 4. 通过 pat.GetRealInfo 获取真实用户信息（昵称、头像）5. API 层直接调用具体服务类，避免中间层
- SendMessage事务处理已优化：参考UserExchangeBonusItem的事务模式，使用tx.Create()直接操作而非repo方法；UpdateConversationLastMessage方法也已修改为使用tx.Model()直接操作，并增加了RowsAffected检查确保更新成功，提高了事务的一致性和错误处理能力
- 帖子编辑接口已添加：新增EditPost接口(/web/v1/posts/edit_content)，允许商家编辑帖子的描述、价格和媒体文件，包含完整的权限验证（商家身份、帖子所有权）和状态检查（违规下架和已删除的帖子不能编辑），编辑后自动清理相关缓存
- 帖子代码已优化复用logic层：新增ValidatePostParams、GetUserPostByID、ValidatePostEditableStatus等通用方法，CreatePost和EditPost方法已重构使用这些复用逻辑，减少代码重复，提高维护性
