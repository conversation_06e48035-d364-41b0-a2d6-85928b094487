package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"strconv"
	"time"

	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service/logic"
	"app_service/apps/platform/common/constant"
	"app_service/global"
	"app_service/pkg/cache"

	"github.com/go-redis/redis/v8"
)

// 测试缓存防护功能
func main() {
	// 初始化Redis连接（需要根据实际配置调整）
	global.REDIS = redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	ctx := context.Background()

	fmt.Println("=== 缓存防护功能测试 ===")
	
	// 测试1：随机TTL防雪崩
	testRandomTTL(ctx)
	
	// 测试2：空值缓存防穿透（仅帖子详情）
	testNullCacheForPostDetail(ctx)
	
	// 测试3：列表缓存不使用空值缓存
	testPostListCache(ctx)
	
	// 测试4：并发测试
	testConcurrentAccess(ctx)
	
	fmt.Println("\n=== 测试完成 ===")
}

// 测试随机TTL防雪崩
func testRandomTTL(ctx context.Context) {
	fmt.Println("\n--- 测试1：随机TTL防雪崩 ---")
	
	// 创建多个缓存，检查TTL是否有随机性
	basePostDetail := &define.GetPostDetailResp{
		ID:          "test-post-1",
		Title:       "测试帖子",
		Description: "测试描述",
	}
	
	var ttls []time.Duration
	
	for i := 0; i < 5; i++ {
		postID := fmt.Sprintf("test-post-%d", i)
		
		// 设置缓存
		err := logic.SetPostDetailCache(ctx, postID, basePostDetail)
		if err != nil {
			log.Printf("设置缓存失败: %v", err)
			continue
		}
		
		// 获取TTL
		cacheKey := constant.GetPostDetailKey(postID)
		ttl := global.REDIS.TTL(ctx, cacheKey).Val()
		ttls = append(ttls, ttl)
		
		fmt.Printf("帖子 %s 的TTL: %v\n", postID, ttl)
	}
	
	// 检查TTL是否有差异（随机性）
	if len(ttls) > 1 {
		hasVariation := false
		for i := 1; i < len(ttls); i++ {
			if ttls[i] != ttls[0] {
				hasVariation = true
				break
			}
		}
		
		if hasVariation {
			fmt.Println("✅ 随机TTL测试通过：TTL存在差异，防雪崩功能正常")
		} else {
			fmt.Println("⚠️  随机TTL测试警告：所有TTL相同，可能随机性不足")
		}
	}
}

// 测试空值缓存防穿透（仅帖子详情）
func testNullCacheForPostDetail(ctx context.Context) {
	fmt.Println("\n--- 测试2：空值缓存防穿透（帖子详情） ---")
	
	nonExistentPostID := "non-existent-post-" + strconv.FormatInt(time.Now().UnixNano(), 10)
	cacheKey := constant.GetPostDetailKey(nonExistentPostID)
	
	// 清除可能存在的缓存
	global.REDIS.Del(ctx, cacheKey)
	
	// 第一次查询缓存（应该未命中）
	postDetail, err := logic.GetPostDetailFromCache(ctx, nonExistentPostID)
	if err != nil {
		log.Printf("查询缓存失败: %v", err)
		return
	}
	
	if postDetail == nil {
		fmt.Println("✅ 第一次查询：缓存未命中，返回nil")
	}
	
	// 模拟设置空值缓存
	err = cache.SetNullCache(ctx, cacheKey)
	if err != nil {
		log.Printf("设置空值缓存失败: %v", err)
		return
	}
	
	// 第二次查询缓存（应该命中空值缓存）
	postDetail, err = logic.GetPostDetailFromCache(ctx, nonExistentPostID)
	if err != nil {
		log.Printf("查询缓存失败: %v", err)
		return
	}
	
	if postDetail == nil {
		fmt.Println("✅ 第二次查询：命中空值缓存，返回nil，防穿透功能正常")
	}
	
	// 检查缓存中确实存在空值标记
	cachedValue := global.REDIS.Get(ctx, cacheKey).Val()
	if cachedValue == cache.NullCacheValue {
		fmt.Println("✅ 缓存中存在空值标记，防穿透设置正确")
	}
}

// 测试列表缓存不使用空值缓存
func testPostListCache(ctx context.Context) {
	fmt.Println("\n--- 测试3：列表缓存不使用空值缓存 ---")
	
	page, size := 1, 10
	cacheKey := constant.GetPostListKey(page, size)
	
	// 清除可能存在的缓存
	global.REDIS.Del(ctx, cacheKey)
	
	// 查询缓存（应该未命中）
	postList, err := logic.GetPostListFromCache(ctx, page, size)
	if err != nil {
		log.Printf("查询列表缓存失败: %v", err)
		return
	}
	
	if postList == nil {
		fmt.Println("✅ 列表缓存未命中，返回nil")
	}
	
	// 设置一个正常的列表缓存
	testPostList := &define.GetPostListResp{
		List: []define.GetPostListData{
			{
				ID:    "test-list-post-1",
				Title: "列表测试帖子1",
			},
		},
		Total: 1,
	}
	
	err = logic.SetPostListCache(ctx, page, size, testPostList)
	if err != nil {
		log.Printf("设置列表缓存失败: %v", err)
		return
	}
	
	// 再次查询缓存（应该命中）
	cachedPostList, err := logic.GetPostListFromCache(ctx, page, size)
	if err != nil {
		log.Printf("查询列表缓存失败: %v", err)
		return
	}
	
	if cachedPostList != nil && len(cachedPostList.List) > 0 {
		fmt.Println("✅ 列表缓存设置和获取正常")
	}
	
	// 检查列表缓存的TTL是否有随机性
	ttl := global.REDIS.TTL(ctx, cacheKey).Val()
	fmt.Printf("列表缓存TTL: %v\n", ttl)
	
	// 验证列表缓存不会设置空值缓存
	cachedValue := global.REDIS.Get(ctx, cacheKey).Val()
	if cachedValue != cache.NullCacheValue {
		fmt.Println("✅ 列表缓存不使用空值缓存机制，符合预期")
	}
}

// 测试并发访问
func testConcurrentAccess(ctx context.Context) {
	fmt.Println("\n--- 测试4：并发访问测试 ---")
	
	postID := "concurrent-test-post"
	cacheKey := constant.GetPostDetailKey(postID)
	
	// 清除缓存
	global.REDIS.Del(ctx, cacheKey)
	
	// 并发查询缓存
	concurrency := 10
	results := make(chan bool, concurrency)
	
	for i := 0; i < concurrency; i++ {
		go func(id int) {
			// 随机延迟
			time.Sleep(time.Duration(rand.Intn(100)) * time.Millisecond)
			
			postDetail, err := logic.GetPostDetailFromCache(ctx, postID)
			if err != nil {
				log.Printf("并发查询 %d 失败: %v", id, err)
				results <- false
				return
			}
			
			// 所有查询都应该返回nil（缓存未命中）
			results <- (postDetail == nil)
		}(i)
	}
	
	// 收集结果
	successCount := 0
	for i := 0; i < concurrency; i++ {
		if <-results {
			successCount++
		}
	}
	
	fmt.Printf("并发测试结果: %d/%d 成功\n", successCount, concurrency)
	
	if successCount == concurrency {
		fmt.Println("✅ 并发访问测试通过")
	} else {
		fmt.Println("⚠️  并发访问测试部分失败")
	}
}
