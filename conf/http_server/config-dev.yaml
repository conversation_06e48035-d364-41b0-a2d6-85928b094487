service:
  name: 'app_service'
  version: '1.0.0'
  address: ":8801"
  token: "c2jk9W3J2mdjqGcL"
  env: "dev"
mysql:
  app_service:
    host: '127.0.0.1'
    port: 3306
    name: 'app_service'
    user: 'root'
    password: '123456'
#     password: 'nnA6MXpPQhqkxxEBLo'
    prefix: ''
    # 时区
    loc: Asia%2FShanghai
redis:
  app_service:
    host: '127.0.0.1'
    port: 6379
    user: ''
    password: ''
#     password: 'A6MXpPQhqkxxEBLo'
    db: 0
mongo:
  uri: '127.0.0.1:27017'
  database: 'admin'
  user: 'admin'
#   password: '123456'
  password: '123456'
  timeout: 20
  maxnum: 100
kafka:
  - name: 'app_service'
    address:
      - 127.0.0.1:9092
logger:
  filename: './logs/info.log'

master_http:
  hosts:
    pat:
      host: "https://sit-wcjs-api.ahbq.com.cn/pat"
      auth: "Bearer UpVroDzmASRxfNvl"
    set:
      host: "https://sit-wcjs-api.ahbq.com.cn/set"
      auth: "Bearer FbsD6iVCltCSb6rE"
    tmt:
      host: "https://sit-wcjs-api.ahbq.com.cn/tmt"
      auth: "Bearer pwhQrrRzPkPv94PK"
    mor:
      host: "https://sit-wcjs-api.ahbq.com.cn/mor"
      auth: "Bearer c5kOw8O6rmq2xhAc"
pbs:
    admin_host: "http://sit-wcjs-api.ahbq.com.cn/goapi/pbs_admin"
yc:
  client_id: "yc_trade_market"
  client_secret: "HXe6DEg2SuB2kc2WaHAsHWRcEVuZDetYSTBP27trRJtC"
  host: "https://sit-openapi-yc.genkimart.ltd/api"
#   host: "http://127.0.0.1:9820"
warn_id: "608bbb4a403700007f00765c"
auth:
  remote_timeout: 10  # 远程API超时时间（秒）
  enable_fallback: false  # 是否启用本地验证降级
  public_key: ""  # JWT公钥（如果启用本地验证）
