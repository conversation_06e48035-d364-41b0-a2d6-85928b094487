package cache

import (
	"context"
	"testing"
)

func TestSetCache(t *testing.T) {
	type args struct {
		ctx   context.Context
		key   string
		value interface{}
		opts  *CacheOptions
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SetCache(tt.args.ctx, tt.args.key, tt.args.value, tt.args.opts); (err != nil) != tt.wantErr {
				t.Errorf("SetCache() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
