{"go.useLanguageServer": true, "go.alternateTools": {"go-langserver": "gopls"}, "gopls": {"experimentalWorkspaceModule": true, "completeUnimported": true, "deepCompletion": true, "matcher": "fuzzy", "staticcheck": true, "usePlaceholders": true, "analyses": {"fieldalignment": false, "shadow": true, "unusedparams": true, "unusedwrite": true}}, "go.lintTool": "golangci-lint", "go.lintOnSave": "package", "go.formatTool": "goimports", "go.gocodeAutoBuild": false, "go.installDependenciesWhenBuilding": true, "go.buildOnSave": "off", "go.vetOnSave": "off"}