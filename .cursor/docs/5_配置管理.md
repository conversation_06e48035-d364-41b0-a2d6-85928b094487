# 5. 配置管理

本文档详细说明了项目的配置管理机制，包括配置文件的结构、多环境支持以及加载方式。

## 5.1. 配置文件位置

- 所有配置文件均存放于项目根目录下的 `conf/` 文件夹中。
- 配置根据服务类型进行划分，例如 `http_server` 的配置位于 `conf/http_server/` 目录下。

## 5.2. 配置文件结构

- 配置文件采用 `YAML` 格式，结构清晰，易于阅读和修改。
- 配置项按功能模块进行分组，例如 `service`, `mysql`, `redis`, `mongo`, `kafka` 等。

**示例 (`config-dev.yaml`):**

```yaml
service:
  name: 'app_service'
  version: '1.0.0'
  address: ":8801"
  env: "dev"

mysql:
  app_service:
    host: '127.0.0.1'
    port: 3306
    # ...其他配置

redis:
  app_service:
    host: '127.0.0.1'
    port: 6379
    # ...其他配置

# ...其他模块配置
```

## 5.3. 多环境管理

项目支持多环境配置，通过不同的文件名来区分环境：

- `config-dev.yaml`: 开发环境
- `config-sit.yaml`: 测试环境
- `config-prod.yaml`: 生产环境

### 加载机制

- 应用程序通过环境变量 `ENV` 来决定加载哪个配置文件。
- 例如，当 `ENV=prod` 时，系统会自动加载 `config-prod.yaml`。
- 这种机制由项目依赖的内部公共库 `e.coding.net/g-dtay0385/common/go-config` 实现，简化了环境切换的复杂度。

## 5.4. 核心配置项说明

- **`service`**: 定义了服务的名称、版本、监听地址等基本信息。
- **`mysql` / `redis` / `mongo`**: 分别定义了关系型数据库、缓存和文档数据库的连接信息。
- **`kafka`**: 定义了 Kafka 消息队列的连接地址。
- **`logger`**: 配置日志文件的输出路径和级别。
- **`master_http` / `pbs` / `yc`**: 定义了项目所依赖的第三方服务的地址和认证信息。
