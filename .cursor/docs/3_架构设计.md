# 3. 架构设计

本项目采用经典的分层架构，确保了代码的低耦合、高内聚和易于维护。整体架构自上而下分为接入层、业务逻辑层和数据存储层。

## 3.1. 整体架构图

```plaintext
+----------------------------------------------------+
|                   接入层 (Entry)                   |
|----------------------------------------------------|
| - HTTP Server (Gin)                                |
| - Task Server (Scheduled/Async Jobs)               |
| - gRPC Server (Inter-service Communication)        |
+------------------------|---------------------------+
                         |
+------------------------v---------------------------+
|                业务逻辑层 (Business)               |
|----------------------------------------------------|
| - 路由 (Router)                                    |
| - 接口 (API)                                       |
| - 服务 (Service)                                   |
| - 数据结构 (Define)                                |
| - 仓储 (Repository) - (部分项目实践)             |
+------------------------|---------------------------+
                         |
+------------------------v---------------------------+
|                通用能力层 (Common)                 |
|----------------------------------------------------|
| - 平台模块 (Platform Apps)                         |
| - 通用工具包 (PKG)                                 |
| - 第三方服务 (Third Party)                         |
+------------------------|---------------------------+
                         |
+------------------------v---------------------------+
|                数据存储层 (Storage)                |
|----------------------------------------------------|
| - MySQL                                            |
| - MongoDB                                          |
| - Redis                                            |
| - Elasticsearch                                    |
+----------------------------------------------------+
```

## 3.2. 分层详解

### 3.2.1. 接入层 (`cmd/`)

- **职责**: 作为系统的统一入口，负责接收外部请求并分发给相应的业务逻辑层处理。
- **组成**:
  - `http_server`: 基于 `Gin` 框架，提供 RESTful API 接口。
  - `task_server`: 负责执行定时任务和异步消息处理，削峰填谷。
  - `grpc_server`: 提供高性能的 RPC 接口，用于服务间通信。

### 3.2.2. 业务逻辑层 (`apps/`)

- **职责**: 实现项目的核心业务功能。该层被划分为多个独立的业务模块（如 `group_buying`, `creation_event`），每个模块都遵循相同的内部结构。
- **模块内部结构**:
  - `router`: 定义路由规则，将请求映射到具体的处理函数。
  - `api`: 定义对外暴露的接口，通常与 `router` 结合使用。
  - `service`: 实现具体的业务逻辑，是功能的核心。
  - `define`: 定义数据传输对象（DTOs），包括请求和响应的结构体。
  - `model`: 定义数据库实体模型。
  - `repository` (可选): 抽象数据访问逻辑，将 `service` 与具体的数据操作解耦。

### 3.2.3. 通用能力层 (`pkg/`, `third_party/`)

- **职责**: 提供可被多个业务模块复用的通用功能和第三方服务集成。
- **组成**:
  - `pkg/`: 存放项目内部的通用工具包，如日志、中间件、数据库客户端、工具函数等。
  - `third_party/`: 封装对外部第三方服务的调用，如腾讯云 COS、极光推送等。

### 3.2.4. 数据存储层

- **职责**: 负责数据的持久化和检索。
- **技术栈**: 包括 MySQL、MongoDB、Redis 和 Elasticsearch，以满足不同场景下的数据存储需求。

## 3.3. 设计原则

- **单一职责**: 每个分层和模块都有明确的职责边界。
- **依赖倒置**: 上层模块不直接依赖下层模块的具体实现，而是依赖于抽象。
- **模块化**: 业务功能按模块划分，便于独立开发、测试和维护。
