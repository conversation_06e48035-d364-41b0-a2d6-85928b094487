# 1. 项目概述

## 1.1. 项目简介

`app_service` 是一个专注于创新业务的后端服务，旨在为各类线上活动和业务模式提供稳定、高效的技术支持。项目采用微服务架构，具备良好的扩展性和可维护性，能够快速响应业务需求。

## 1.2. 核心业务模块

项目当前主要包含以下核心业务模块：

- **百人团 (`group_buying`)**: 支持团购、拼团等社交电商玩法，提供完整的商品、订单、支付和成团逻辑。

- **造物活动 (`creation_event`)**: 围绕线上创意活动设计，支持用户参与、作品提交、评选、发奖等全流程功能。

- **其他业务**: 项目架构设计支持未来快速扩展新的业务模块。

## 1.3. 设计目标

- **业务驱动**: 技术服务于业务，快速迭代，支持业务模式的探索与创新。
- **高可用性**: 通过微服务架构和容错设计，确保核心服务的高可用性。
- **可扩展性**: 采用模块化设计，方便新业务的接入和已有业务的扩展。
- **易维护性**: 清晰的代码结构和统一的开发规范，降低长期维护成本。
