# 4. 环境与部署指南

本文档旨在为开发人员提供清晰的指导，涵盖项目的本地开发环境配置、服务启动以及线上部署流程。

## 4.1. 本地开发环境

### 4.1.1. 前提条件

- **Go**: 版本 `1.19` 或更高。
- **Docker**: 用于构建和运行服务镜像。
- **Git**: 用于代码版本控制。
- **私有仓库访问**: 确保已配置好访问 `e.coding.net` 的 SSH 密钥，以便拉取私有依赖。

### 4.1.2. 启动服务

项目没有提供直接通过 `make` 命令启动本地服务的脚本，推荐通过 Go 命令直接运行 `main.go` 文件来启动服务。

1.  **启动 HTTP 服务**:

    ```bash
    go run cmd/http_server/main.go
    ```

2.  **启动 Task 服务**:

    ```bash
    go run cmd/task_server/main.go
    ```

在启动前，请确保本地的 `conf/http_server/config-dev.yaml` 文件中的数据库、Redis 等配置是正确的。

## 4.2. 部署流程 (Docker)

项目通过 `Dockerfile` 定义了标准的容器化部署流程。该流程分为构建和运行两个阶段，确保了最终镜像的轻量化。

### 4.2.1. 构建 Docker 镜像

在项目根目录下执行以下命令来构建镜像：

```bash
# 替换 your-image-name 为你想要的镜像名称和标签
docker build -t your-image-name:latest .
```

**构建过程详解**: 

1.  **Builder 阶段**: 
    - 使用 `golang:1.19` 作为基础镜像。
    - 配置 `GOPRIVATE` 以支持私有仓库的依赖下载。
    - 运行 `go mod tidy` 下载所有依赖。
    - 使用 `go build` 命令将 `cmd/http_server/main.go` 编译为二进制文件。

2.  **Runtime 阶段**: 
    - 使用轻量的 `alpine:latest` 作为最终镜像的基础。
    - 设置时区为 `Asia/Shanghai`。
    - 从 `builder` 阶段复制已编译的二进制文件和 `conf` 目录。
    - 暴露服务端口 `8001`。

### 4.2.2. 运行 Docker 容器

使用以下命令来启动服务容器：

```bash
# -p 8001:8001 将容器的 8001 端口映射到主机的 8001 端口
# -e ENV=prod 指定运行环境为生产环境，容器会加载对应的配置文件
docker run -d -p 8001:8001 -e ENV=prod your-image-name:latest
```

通过 `-e` 参数可以灵活切换不同的运行环境（如 `dev`, `sit`, `prod`），容器会根据环境变量加载 `conf` 目录下对应的配置文件。

## 4.3. 其他命令

- **代码检查**:

  ```bash
  make check
  ```

- **生成 API 文档**:

  ```bash
  make swag
  ```
