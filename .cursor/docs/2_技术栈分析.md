# 技术栈分析

本文档旨在梳理项目当前使用的主要技术、框架和第三方库，以便于团队成员快速了解项目技术背景。

## 1. 核心技术

| 分类 | 技术/框架 | 版本/说明 |
| --- | --- | --- |
| **编程语言** | Go | `1.19` |
| **Web 框架** | Gin | `v1.10.0`，用于构建高性能的 HTTP 服务。 |
| **微服务框架** | Go-Micro | `v4.10.2`，作为项目微服务架构的基础。 |

## 2. 数据存储

| 分类 | 技术/库 | 说明 |
| --- | --- | --- |
| **关系型数据库** | MySQL | 使用 `gorm.io/driver/mysql` 作为驱动，`GORM` 作为 ORM。 |
| **NoSQL 数据库** | MongoDB | 使用 `go.mongodb.org/mongo-driver` 进行数据操作。 |
| **缓存** | Redis | 使用 `github.com/go-redis/redis/v8` 与 Redis 服务交互。 |
| **搜索引擎** | Elasticsearch | `v7` 版本，通过 `github.com/olivere/elastic/v7` 客户端集成。 |

## 3. 中间件与消息队列

| 分类 | 技术/库 | 说明 |
| --- | --- | --- |
| **消息队列** | Kafka | 通过内部封装的 `go-kafka` 库实现异步消息处理。 |

## 4. 可观测性

| 分类 | 技术/库 | 说明 |
| --- | --- | --- |
| **日志** | `go-logger` | 项目内部统一的日志库。 |
| **分布式追踪** | OpenTelemetry | 用于实现跨服务的链路追踪，提升系统可观测性。 |

## 5. 第三方服务与库

| 分类 | 服务/库 | 说明 |
| --- | --- | --- |
| **API 文档** | Swagger | 使用 `swaggo/swag` 自动生成和管理 API 文档。 |
| **云服务** | 腾讯云 | 集成了腾讯云的 **对象存储 (COS)** 和 **文本内容安全 (TMS)** 服务。 |
| **Excel 操作** | Excelize | `v2.6.1`，用于处理 Excel 文件的导入导出。 |
| **唯一 ID 生成** | Snowflake | `github.com/bwmarrin/snowflake`，用于生成分布式唯一 ID。 |
| **工具库** | Lancet, Lo | 提供了丰富的工具函数，简化开发。 |

## 6. 内部依赖

项目大量依赖于 `e.coding.net/g-dtay0385/common/` 仓库下的内部公共库，例如：

- `go-config`: 统一配置管理
- `go-initialize`: 服务初始化
- `go-middleware`: 通用中间件
- `go-util`: 通用工具函数

这些库构成了公司内部的标准化开发平台，确保了项目间的一致性和开发效率。
