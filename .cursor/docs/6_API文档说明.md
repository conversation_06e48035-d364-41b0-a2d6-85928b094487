# 6. API 文档说明

项目使用 [Swagger](https://swagger.io/) 来构建和管理 API 文档，以确保前后端开发人员能够清晰、高效地进行协作。

## 6.1. 技术方案

- **库**: `github.com/swaggo/swag` 及其与 `Gin` 集成的 `gin-swagger`。
- **原理**: 通过在代码中编写特定格式的注释，`swag` 工具可以自动解析这些注释并生成符合 OpenAPI 规范的 `swagger.json` 和 `swagger.yaml` 文件。

## 6.2. 如何编写 API 注释

为了让 `swag` 工具能够正确识别，您需要在每个 API 的 `Handler` 函数前添加块注释。注释内容包括：

- API 摘要 (`@Summary`)
- 描述 (`@Description`)
- 路由 (`@Router`)
- 参数 (`@Param`)
- 成功响应 (`@Success`)
- 失败响应 (`@Failure`)

**示例:**

```go
// @Summary      获取用户信息
// @Description  根据用户ID获取详细信息
// @Tags         User
// @Accept       json
// @Produce      json
// @Param        id   path      int  true  "User ID"
// @Success      200  {object}  model.User
// @Failure      400  {object}  httputil.HTTPError
// @Failure      404  {object}  httputil.HTTPError
// @Router       /users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
    // ... handler logic
}
```

## 6.3. 生成和更新 API 文档

当您添加了新的 API 或修改了现有 API 的注释后，需要手动执行 `make` 命令来更新 Swagger 文档。

```bash
make swag
```

此命令会：

1.  扫描 `cmd/http_server/main.go` 作为入口，分析整个项目的代码。
2.  解析所有符合规范的 API 注释。
3.  在 `docs/` 目录下生成或更新 `app_service_swagger.json` 和 `app_service_swagger.yaml` 文件。

## 6.4. 访问 API 文档

项目已集成 `gin-swagger` 中间件，在服务启动后，即可通过浏览器访问交互式的 API 文档页面。

- **访问地址**: `http://<服务地址>/swagger/index.html`

例如，如果本地服务启动在 `http://127.0.0.1:8801`，那么 API 文档地址就是 `http://127.0.0.1:8801/swagger/index.html`。

在这个页面上，您可以：

- 查看所有 API 的列表和详细信息。
- 了解每个接口的请求参数和响应结构。
- 直接在页面上发起 API 请求，进行在线调试。
